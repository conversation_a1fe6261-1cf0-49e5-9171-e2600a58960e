import {
  Preset,
  defineConfig,
  presetAttributify,
  presetIcons,
  transformerDirectives,
  transformerVariantGroup,
} from "unocss";

import {
  presetApplet,
  presetRemRpx,
  transformerApplet,
  transformerAttributify,
} from "unocss-applet";

const isH5 = process.env?.UNI_PLATFORM === "h5";
const isMp = process.env?.UNI_PLATFORM?.startsWith("mp") ?? false;

const presets: Preset[] = [];
if (!isMp) {
  /**
   * you can add `presetAttributify()` here to enable unocss attributify mode prompt
   * although preset is not working for applet, but will generate useless css
   * 为了不生产无用的css,要过滤掉 applet
   */
  // 支持css class属性化，eg: `<button bg="blue-400 hover:blue-500 dark:blue-500 dark:hover:blue-600" text="sm white">attributify Button</button>`
  presets.push(presetAttributify());
}
if (!isH5) {
  presets.push(presetRemRpx());
}
export default defineConfig({
  presets: [
    presetApplet({ enable: !isH5 }),
    ...presets,
    // 支持图标，需要搭配图标库，eg: @iconify-json/carbon, 使用 `<button class="i-carbon-sun dark:i-carbon-moon" />`
    presetIcons({
      scale: 1.2,
      warn: true,
      extraProperties: {
        display: "inline-block",
        "vertical-align": "middle",
      },
    }),
  ],
  /**
   * 自定义快捷语句
   * @see https://github.com/unocss/unocss#shortcuts
   */
  shortcuts: [["center", "flex justify-center items-center"]],
  transformers: [
    // 启用 @apply 功能
    transformerDirectives(),
    // 启用 () 分组功能
    // 支持css class组合，eg: `<div class="hover:(bg-gray-400 font-medium) font-(light mono)">测试 unocss</div>`
    transformerVariantGroup(),
    // Don't change the following order
    transformerAttributify({
      // 解决与第三方框架样式冲突问题
      prefixedOnly: true,
      prefix: "fg",
    }),
    transformerApplet(),
  ],
  rules: [
    ["primary-50", { 'color': "var(--primary-50)" }],
    ["primary-100", { 'color': "var(--primary-100)" }],
    ["primary-200", { 'color': "var(--primary-200)" }],
    ["primary-300", { 'color': "var(--primary-300)" }],
    ["primary-400", { 'color': "var(--primary-400)" }],
    ["primary-500", { 'color': "var(--primary-500)" }],
    ["primary-600", { 'color': "var(--primary-600)" }],
    ["primary-700", { 'color': "var(--primary-700)" }],
    ["primary-800", { 'color': "var(--primary-800)" }],
    ["primary-900", { 'color': "var(--primary-900)" }],
    ["primary-950", { 'color': "var(--primary-950)" }],
    ["primary", { 'color': "var(--primary)" }],
    // 更新颜色规则，使用新的颜色系统
    ["text-base", { 'color': "var(--text-base)" }],          // 替换 #333/#121212 为新的主文本色
    ["text-secondary", { 'color': "var(--text-secondary)" }], // 替换 #666 为新的次要文本色
    ["text-info", { 'color': "var(--text-info)" }],           // 描述文本色
    ["text-grey", { 'color': "var(--text-grey)" }],           // 替换 #999 为新的灰色文本
    ["text-disable", { 'color': "var(--text-disable)" }],     // 禁用文本色
    ["text-inverse", { 'color': "var(--text-inverse)" }],     // 反色文本
    ["text-red", { 'color': "var(--text-red)" }],             // 红色文本
    ["text-green", { 'color': "var(--text-green)" }],         // 绿色文本
    ["text-blue", { 'color': "var(--text-blue)" }],           // 蓝色文本
    ["text-yellow", { 'color': "var(--text-yellow)" }],       // 黄色文本
    ["text-purple", { 'color': "var(--text-purple)" }],       // 紫色文本

    // 背景颜色
    ["bg-page", { "background-color": "var(--bg-page)" }],   // 页面背景色，替换 #f5f5f5
    ["bg-card", { "background-color": "var(--bg-card)" }],   // 卡片背景色
    ["bg-tag", { "background-color": "var(--bg-tag)" }],     // 标签背景色
    ["bg-search", { "background-color": "var(--bg-search)" }], // 搜索框背景色
    ["bg-primary", { "background-color": "var(--primary)" }], // 主色背景
    ["bg-disable", { "background-color": "var(--bg-disabled)" }], // 禁用背景色

    ["card", {
      "background-color": "#feffff",
      "border-radius": "20rpx",
      "box-shadow": "0 4rpx  8rpx rgba(195, 195, 210, 0.1)",
      "padding": "24rpx",
    },
    ],

    [
      "flex-x",
      {
        "display": "flex",
        "flex-direction": "row",
        "flex-wrap": "wrap",
        "flex-grow": 1,
        "flex-shrink": 1,
        "flex-basis": "auto",
      },
    ],
    [
      "flex-y",
      {
        "display": "flex",
        "flex-direction": "column",
      },
    ],
    [
      "flex-x-center",
      {
        "display": "flex",
        "flex-direction": "row",
        "align-items": "center",
      },
    ],
    [
      "flex-x-between",
      {
        "display": "flex",
        "flex-direction": "row",
        "justify-content": "space-between",
        "align-items": "center",
      },
    ],
    [
      "flex-x-around",
      {
        "display": "flex",
        "flex-direction": "row",
        "justify-content": "space-around",
        "align-items": "center",
      },
    ],
    [
      "flex-y-center",
      {
        "display": "flex",
        "flex-direction": "column",
        "justify-content": "center",
        "align-items": "center",
      },
    ],
    [
      "flex-y-between",
      {
        "display": "flex",
        "flex-direction": "column",
        "justify-content": "space-between",
        "align-items": "center",
      },
    ],

    ["safe-area", { "padding-bottom": "env(safe-area-inset-bottom)" }],
    ["text-13", { "font-size": "26rpx" }],
    ["text-14", { "font-size": "28rpx" }],
    ["text-15", { "font-size": "30rpx" }],
    ["text-16", { "font-size": "32rpx" }],
    ["text-17", { "font-size": "34rpx" }],
    ["text-18", { "font-size": "36rpx" }],
    ["text-20", { "font-size": "40rpx" }],
    ["text-22", { "font-size": "44rpx" }],
    ["text-24", { "font-size": "48rpx" }],
    ["text-26", { "font-size": "52rpx" }],
    ["text-28", { "font-size": "56rpx" }],
    ["spacing-sm", { "margin": "8rpx" }],
    ["spacing-md", { "margin": "16rpx" }],
    ["spacing-lg", { "margin": "24rpx" }],
    ["spacing-xl", { "margin": "32rpx" }],
    ["spacing-2xl", { "margin": "40rpx" }],
    ["spacing-3xl", { "margin": "48rpx" }],
    ["color-main", { color: "$text-base" }],
    ["color-secondary", { color: "$text-secondary" }],
    ["color-info", { color: "$text-info" }],
    ["color-grey", { color: "$text-grey" }],
    ["color-disable", { color: "$text-disable" }],
    ["color-inverse", { color: "$text-inverse" }],
  ],
});

/**
 * 最终这一套组合下来会得到：
 * mp 里面：mt-4 => margin-top: 32rpx
 * h5 里面：mt-4 => margin-top: 1rem
 */
