import { ref, computed } from 'vue'
import { useRequest } from 'alova/client'
import AuthApi from '@/api/auth'
import { useUserStore } from '@/stores/user'
import { useGlobalStore } from '@/stores/global'
import { showSuccessToast } from '@/utils'

/**
 * 认证服务 - 封装登录相关逻辑
 */
export class AuthService {
  private userStore = useUserStore()
  private globalStore = useGlobalStore()

  /**
   * 初始化登录检查
   * 在小程序启动时调用，检查用户登录状态
   */
  async initLoginCheck(): Promise<void> {
    try {
      // 1. 获取微信登录code
      const loginResult = await this.getWechatLoginCode()
      if (!loginResult.success) {
        console.warn('获取微信登录code失败:', loginResult.error)
        return
      }

      // 2. 检查用户是否已注册
      const checkResult = await this.checkUserRegistration(loginResult.code!)
      if (!checkResult.success) {
        console.warn('检查用户注册状态失败:', checkResult.error)
        return
      }

      // 3. 根据检查结果处理
      this.globalStore.setUserRegistered(checkResult.data!.isRegistered)

      if (checkResult.data!.isRegistered && checkResult.data!.hasPhone) {
        // 用户已注册且有手机号，执行自动登录
        await this.performWechatLogin(loginResult.code!)
      }
      // 如果未注册或没有手机号，等待用户手动触发登录

    } catch (error) {
      console.error('初始化登录检查失败:', error)
    }
  }

  /**
   * 获取微信登录code
   */
  private async getWechatLoginCode(): Promise<{ success: boolean; code?: string; error?: string }> {
    return new Promise((resolve) => {
      uni.login({
        provider: 'weixin',
        success: (res) => {
          if (res.code) {
            this.globalStore.setLoginCode(res.code)
            resolve({ success: true, code: res.code })
          } else {
            resolve({ success: false, error: '获取微信登录code失败' })
          }
        },
        fail: (err) => {
          resolve({ success: false, error: err.errMsg || '微信登录失败' })
        }
      })
    })
  }

  /**
   * 检查用户注册状态
   */
  private async checkUserRegistration(code: string): Promise<{
    success: boolean;
    data?: { isRegistered: boolean; hasPhone: boolean };
    error?: string
  }> {
    try {
      const deviceId = AuthApi.getDeviceId()
      const response = await AuthApi.checkUserByCode({ code, deviceId }).send()

      return {
        success: true,
        data: {
          isRegistered: response.is_registered,
          hasPhone: response.has_phone
        }
      }
    } catch (error: any) {
      return {
        success: false,
        error: error.message || '检查用户注册状态失败'
      }
    }
  }

  /**
   * 执行微信登录（已注册用户）
   */
  private async performWechatLogin(loginCode: string): Promise<{ success: boolean; error?: string }> {
    try {
      const deviceId = AuthApi.getDeviceId()
      const response = await AuthApi.wechatLogin({ loginCode, deviceId }).send()

      // 保存用户信息和token
      this.userStore.setUserInfo(response.user, response.access_token)

      uni.showToast({
        title: '登录成功',
        icon: 'success',
        duration: 1500
      })

      return { success: true }
    } catch (error: any) {
      return {
        success: false,
        error: error.message || '登录失败'
      }
    }
  }

  /**
   * 处理用户手动登录（未注册用户或需要授权手机号）
   */
  async handleManualLogin(): Promise<{ success: boolean; error?: string }> {
    try {
      // 1. 重新获取登录code（确保code有效）
      const loginResult = await this.getWechatLoginCode()
      if (!loginResult.success) {
        return { success: false, error: loginResult.error }
      }

      // 2. 获取手机号授权
      const phoneResult = await this.getPhoneAuthorization()
      if (!phoneResult.success) {
        return { success: false, error: phoneResult.error }
      }

      // 3. 执行登录
      const deviceId = AuthApi.getDeviceId()
      const response = await AuthApi.wechatLogin({
        loginCode: loginResult.code!,
        phoneCode: phoneResult.code!,
        deviceId
      }).send()

      // 4. 保存用户信息
      this.userStore.setUserInfo(response.user, response.access_token)

      uni.showToast({
        title: '登录成功',
        icon: 'success',
        duration: 1500
      })

      return { success: true }
    } catch (error: any) {
      return {
        success: false,
        error: error.message || '登录失败'
      }
    }
  }

  /**
   * 获取手机号授权
   */
  private async getPhoneAuthorization(): Promise<{ success: boolean; code?: string; error?: string }> {
    return new Promise((resolve) => {
      uni.requestSubscribeMessage({
        tmplIds: [], // 这里放置模板消息ID
        success: () => {
          // 手机号授权逻辑
          // 注意：实际的手机号获取需要在button组件的@getphonenumber事件中处理
          // 这里只是示例流程
          resolve({ success: false, error: '请使用授权按钮获取手机号' })
        },
        fail: (err) => {
          resolve({ success: false, error: err.errMsg || '授权失败' })
        }
      })
    })
  }

  /**
   * 处理手机号授权事件（从button组件调用）
   */
  async handlePhoneAuthorization(detail: any): Promise<{ success: boolean; code?: string; error?: string }> {
    if (detail.errMsg === 'getPhoneNumber:ok') {
      return { success: true, code: detail.code }
    } else {
      return { success: false, error: '用户拒绝授权手机号' }
    }
  }

  /**
   * 检查登录状态
   */
  isLoggedIn(): boolean {
    return this.userStore.isLogin
  }

  /**
   * 显示登录弹窗
   * 从 navigation.ts 迁移而来的业务逻辑
   */
  showLoginModal(): void {
    uni.showModal({
      title: '请先登录',
      content: '登录后即可使用此功能',
      confirmText: '立即登录',
      cancelText: '取消',
      success: (res) => {
        if (res.confirm) {
          this.startLoginProcess()
        }
      }
    })
  }

  /**
   * 直接启动登录流程，不显示确认对话框
   * 适用于用户主动点击登录相关元素的场景
   * 从 navigation.ts 迁移而来的业务逻辑
   */
  startLoginProcess(): void {
    // 直接获取微信登录code并显示隐私弹窗
    uni.login({
      provider: 'weixin',
      success: (loginRes) => {
        // 保存新的loginCode到全局store
        this.globalStore.setLoginCode(loginRes.code)
        // 显示隐私弹窗进行登录
        this.globalStore.showPrivacyPopup()
      },
      fail: (err) => {
        console.error('微信登录失败:', err)
        uni.showToast({
          title: '获取登录信息失败，请重试',
          icon: 'none'
        })
      }
    })
  }

  /**
   * 退出登录
   */
  logout(): void {
    this.userStore.clearUserInfo()
    this.globalStore.setUserRegistered(false)
    this.globalStore.setHideLoginTip(false) // 重新显示登录提示

    uni.showToast({
      title: '已退出登录',
      icon: 'success',
      duration: 1500
    })
  }
}

// 导出单例
export const authService = new AuthService()

// 兼容性导出 - 为了支持从 navigation.ts 迁移的函数调用
export const showLoginModal = () => authService.showLoginModal()
export const startLoginProcess = () => authService.startLoginProcess()

// ====================================================================
// 现代化 Composable 函数 (useRequest 架构)
// ====================================================================

/**
 * 用户检查 Composable
 * @description 检查用户是否已注册并处理相应逻辑
 */
export function useUserCheck() {
  const globalStore = useGlobalStore()

  const { loading: isChecking, send: checkUser } = useRequest(
    (code: string) => {
      const deviceId = AuthApi.getDeviceId()
      return AuthApi.checkUserByCode({ code, deviceId })
    },
    { immediate: false }
  ).onSuccess((response) => {
    globalStore.setUserRegistered(response.data.is_registered)
    // 如果已注册且有手机号，可以自动登录
    if (response.data.is_registered && response.data.has_phone) {
      // 触发自动登录逻辑
    }
  })

  return {
    isChecking,
    checkUser,
  }
}

/**
 * 微信登录 Composable
 * @description 处理微信登录流程
 */
export function useWechatLogin() {
  const userStore = useUserStore()

  const { loading: isLoggingIn, send: login } = useRequest(
    (data: { loginCode: string; phoneCode?: string; deviceId?: string }) => AuthApi.wechatLogin(data),
    { immediate: false }
  ).onSuccess((response) => {
    // 保存用户信息和token
    userStore.setUserInfo(response.data.user, response.data.access_token)
    showSuccessToast('登录成功')
  })

  return {
    isLoggingIn,
    login,
  }
}

/**
 * 设备ID管理 Composable
 * @description 管理设备ID的获取和重置
 */
export function useDeviceId() {
  const deviceId = ref<string>('')

  const getDeviceId = () => {
    deviceId.value = AuthApi.getDeviceId()
    return deviceId.value
  }

  const resetDeviceId = () => {
    deviceId.value = AuthApi.resetDeviceId()
    return deviceId.value
  }

  // 初始化时获取设备ID
  getDeviceId()

  return {
    deviceId: computed(() => deviceId.value),
    getDeviceId,
    resetDeviceId,
  }
}

/**
 * 登录状态 Composable
 * @description 管理用户登录状态检查
 */
export function useLoginStatus() {
  const userStore = useUserStore()
  const globalStore = useGlobalStore()

  const isLoggedIn = computed(() => userStore.isLogin)
  const isRegistered = computed(() => globalStore.isUserRegistered)

  const logout = () => {
    userStore.clearUserInfo()
    globalStore.setUserRegistered(false)
    globalStore.setHideLoginTip(false)
    showSuccessToast('已退出登录')
  }

  return {
    isLoggedIn,
    isRegistered,
    logout,
  }
}