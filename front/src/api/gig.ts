/**
 * Gig 模块 API 定义 - 命名空间模式
 * 职责：只负责定义与后端接口对应的 alova Method 实例
 * 不包含任何业务逻辑，保持纯粹
 */
import request from '@/utils/network/client'
import type { ApiResponse } from '@/constants/response'
import type {
    Gig,
    CheckApplicationStatusResponse,
    ApplyGigRequest,
    ListGigsRequest,
    GigApplication,
    UpdateApplicationStatusRequest,
    CreateGigRequest,
    MonthlyStatsResponse,
    DailyGigsAndApplicationsResponse,
} from '@/types/gig'
import { PaginatedResponse } from '@/types/common'

/**
 * Gig API 命名空间类
 * 采用静态方法组织，提供清晰的模块边界
 */
class GigApi {
    /**
     * 创建一个新的零工
     * @param data - 创建零工所需的数据
     * @returns alova Method 实例
     */
    static create = (data: CreateGigRequest) => {
        return request.Post<{ gigId: number }>('/gigs', data)
    }

    /**
     * 更新一个零工
     * @param gigId - 要更新的零工 ID
     * @param data - 更新的数据
     * @returns alova Method 实例
     */
    static update = (gigId: number, data: Partial<CreateGigRequest>) => {
        return request.Put<{ message: string }>(`/gigs/${gigId}`, data)
    }

    /**
     * 删除一个我发布的零工
     * @param gigId - 要删除的零工 ID
     * @returns alova Method 实例
     */
    static delete = (gigId: number) => {
        return request.Delete(`/gigs/${gigId}`)
    }

    /**
     * 获取零工详情
     * @param id - 零工 ID
     * @returns 返回一个包含完整响应的 alova Method 实例
     */
    static detail = (id: number) => {
        return request.Get<Gig>(`/gigs/${id}`)
    }

    // ====================================================================
    // 列表查询
    // ====================================================================

    /**
     * 获取零工列表 (用于 seeker 页面)
     * @param params - 列表请求参数，如分页、关键词、排序等
     * @returns alova Method 实例
     */
    static list = (params: ListGigsRequest) => {
        return request.Get<PaginatedResponse<Gig>>('/gigs', { params })
    }

    /**
     * 获取我发布的零工列表 (用于 manage 页面)
     * @param params - 列表请求参数
     * @returns alova Method 实例
     */
    static listMine = (params: ListGigsRequest) => {
        return request.Get<PaginatedResponse<Gig>>('/gigs/manage', { params })
    }

    /**
     * 获取特定零工的申请列表 (用于 applicants 页面)
     * @param gigId - 零工 ID
     * @param params - 分页等参数
     * @returns alova Method 实例
     */
    static getApplications = (gigId: number, params: { page: number, page_size: number }) => {
        return request.Get<PaginatedResponse<GigApplication>>(`/gigs/${gigId}/applications`, { params })
    }

    /**
     * 获取我收到的申请列表 (用于 recruiter 页面)
     * @param params - 分页等参数
     * @returns alova Method 实例
     */
    static listMyApplications = (params: { page: number, page_size: number }) => {
        return request.Get<PaginatedResponse<{ list: GigApplication[], total: number }>>('/gigs/applications/my', { params })
    }

    // ====================================================================
    // 申请相关
    // ====================================================================

    /**
     * 申请零工
     * @param payload - 申请所需的数据
     * @returns 返回一个默认只包含 data 的 alova Method 实例
     */
    static apply = (payload: ApplyGigRequest) => {
        return request.Post<{ message: string; application_id: number }>('/gigs/apply', payload)
    }

    /**
     * 检查用户对特定零工的申请状态
     * @param gigId - 零工 ID
     * @returns 返回一个默认只包含 data 的 alova Method 实例
     */
    static checkApplicationStatus = (gigId: number) => {
        return request.Get<CheckApplicationStatusResponse>(
            `/gigs/${gigId}/application-status`,
        )
    }

    // ====================================================================
    // 管理功能
    // ====================================================================

    /**
     * 审核一个申请 (录用/拒绝)
     * @param params - 包含 application_id 和 new_status
     * @returns alova Method 实例
     */
    static reviewApplication = (params: UpdateApplicationStatusRequest) => {
        return request.Post('/gigs/applications/review', params)
    }

    // ====================================================================
    // 统计功能
    // ====================================================================

    /**
     * 获取月度零工统计
     * @param year - 年份
     * @param month - 月份
     * @returns alova Method 实例
     */
    static getMonthlyStats = (year: number, month: number) => {
        return request.Get<MonthlyStatsResponse>(`/gigs/calendar/monthly?year=${year}&month=${month}`)
    }

    /**
     * 获取指定日期的零工列表和申请列表
     * @param date - 日期字符串 (YYYY-MM-DD)
     * @returns alova Method 实例
     */
    static getDailyGigs = (date: string) => {
        return request.Get<DailyGigsAndApplicationsResponse>(`/gigs/calendar/daily?date=${date}`)
    }
}

// ====================================================================
// 导出配置
// ====================================================================

// 默认导出命名空间类
export default GigApi


