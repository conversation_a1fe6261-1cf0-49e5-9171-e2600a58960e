/**
 * 通用格式化工具函数
 * 提供各种数据格式化功能
 */

// ====================================================================
// 距离格式化
// ====================================================================

/**
 * 格式化距离显示
 * @param distance 距离（米）
 */
export function formatDistance(distance?: number): string {
    if (!distance) return '';

    if (distance < 1000) {
        return `${distance}m`;
    } else {
        return `${(distance / 1000).toFixed(1)}km`;
    }
}
// ====================================================================
// 金额格式化
// ====================================================================

/**
 * 格式化金额，保留两位小数
 * @param amount 金额值
 */
export function formatMoney(amount: number | string): string {
    const num = typeof amount === 'string' ? parseFloat(amount) : amount;
    return num.toFixed(2).replace(/\B(?=(\d{3})+(?!\d))/g, ',');
}

/**
 * 格式化金额（分转元）
 * @param cents 金额（分）
 */
export function formatMoneyFromCents(cents: number): string {
    return formatMoney(cents / 100);
}

/**
 * 格式化金额区间
 * @param min 最小金额
 * @param max 最大金额
 * @param unit 单位
 */
export function formatMoneyRange(min?: number, max?: number, unit: string = '元'): string {
    if (!min && !max) return '';

    if (min && max) {
        if (min === max) {
            return `${formatMoney(min)}${unit}`;
        } else {
            return `${formatMoney(min)}-${formatMoney(max)}${unit}`;
        }
    } else if (min) {
        return `${formatMoney(min)}${unit}起`;
    } else if (max) {
        return `${formatMoney(max)}${unit}以下`;
    }

    return '';
}

// ====================================================================
// 数量格式化
// ====================================================================

/**
 * 格式化数量，大数字转换为K、M等
 * @param num 数量
 */
export function formatCount(num: number): string {
    if (num < 1000) {
        return num.toString();
    } else if (num < 1000000) {
        return (num / 1000).toFixed(1) + 'K';
    } else if (num < 1000000000) {
        return (num / 1000000).toFixed(1) + 'M';
    } else {
        return (num / 1000000000).toFixed(1) + 'B';
    }
}

/**
 * 格式化百分比
 * @param value 数值
 * @param total 总数
 * @param decimals 小数位数
 */
export function formatPercentage(value: number, total: number, decimals: number = 1): string {
    if (total === 0) return '0%';
    return ((value / total) * 100).toFixed(decimals) + '%';
}

/**
 * 格式化进度显示
 * @param current 当前值
 * @param total 总值
 */
export function formatProgress(current: number, total: number): string {
    return `${current}/${total}`;
}

// ====================================================================
// 年龄格式化
// ====================================================================

/**
 * 格式化年龄范围
 * @param ageMin 最小年龄
 * @param ageMax 最大年龄
 */
export function formatAgeRange(ageMin?: number, ageMax?: number): string {
    if (!ageMin && !ageMax) return '年龄不限';

    if (ageMin && ageMax) {
        if (ageMin === ageMax) {
            return `${ageMin}岁`;
        } else {
            return `${ageMin}-${ageMax}岁`;
        }
    } else if (ageMin) {
        return `${ageMin}岁以上`;
    } else if (ageMax) {
        return `${ageMax}岁以下`;
    }

    return '年龄不限';
}

// ====================================================================
// 文本格式化
// ====================================================================

/**
 * 截断文本
 * @param text 文本
 * @param maxLength 最大长度
 * @param suffix 省略号
 */
export function truncateText(text: string, maxLength: number, suffix: string = '...'): string {
    if (!text || text.length <= maxLength) {
        return text;
    }
    return text.substring(0, maxLength) + suffix;
}

/**
 * 格式化描述文本（HTML标签转换）
 * @param html HTML字符串
 * @param maxLength 最大长度
 */
export function formatDescription(html: string, maxLength?: number): string {
    if (!html) return '';

    // 简单的HTML标签移除
    const text = html.replace(/<[^>]*>/g, '');

    if (maxLength) {
        return truncateText(text, maxLength);
    }

    return text;
}

// ====================================================================
// 列表格式化
// ====================================================================

/**
 * 格式化标签列表
 * @param tags 标签数组
 * @param maxCount 最大显示数量
 */
export function formatTagList(tags: string[], maxCount?: number): string[] {
    if (!tags || tags.length === 0) return [];

    if (maxCount && tags.length > maxCount) {
        return [...tags.slice(0, maxCount), `+${tags.length - maxCount}`];
    }

    return tags;
}

/**
 * 格式化名称列表
 * @param names 名称数组
 * @param maxCount 最大显示数量
 * @param separator 分隔符
 */
export function formatNameList(names: string[], maxCount: number = 3, separator: string = ', '): string {
    if (!names || names.length === 0) return '';

    if (names.length <= maxCount) {
        return names.join(separator);
    } else {
        return names.slice(0, maxCount).join(separator) + ` 等${names.length}人`;
    }
}

// ====================================================================
// 文件大小格式化
// ====================================================================

/**
 * 格式化文件大小
 * @param bytes 字节数
 * @param decimals 小数位数
 */
export function formatFileSize(bytes: number, decimals: number = 2): string {
    if (bytes === 0) return '0 B';

    const k = 1024;
    const dm = decimals < 0 ? 0 : decimals;
    const sizes = ['B', 'KB', 'MB', 'GB', 'TB'];

    const i = Math.floor(Math.log(bytes) / Math.log(k));

    return parseFloat((bytes / Math.pow(k, i)).toFixed(dm)) + ' ' + sizes[i];
}

// ====================================================================
// 评分格式化
// ====================================================================

/**
 * 格式化评分显示
 * @param score 评分
 * @param maxScore 最高分
 * @param showMax 是否显示最高分
 */
export function formatScore(score: number, maxScore: number = 5, showMax: boolean = false): string {
    const formattedScore = score.toFixed(1);

    if (showMax) {
        return `${formattedScore}/${maxScore}`;
    }

    return formattedScore;
}

/**
 * 格式化星级显示
 * @param score 评分
 * @param maxScore 最高分
 */
export function formatStarRating(score: number, maxScore: number = 5): string {
    const fullStars = Math.floor(score);
    const hasHalfStar = score % 1 >= 0.5;
    const emptyStars = maxScore - fullStars - (hasHalfStar ? 1 : 0);

    return '★'.repeat(fullStars) + (hasHalfStar ? '☆' : '') + '☆'.repeat(emptyStars);
}

// ====================================================================
// 单位格式化
// ====================================================================

/**
 * 格式化数量单位
 * @param count 数量
 * @param unit 单位
 * @param zeroText 零值显示文本
 */
export function formatUnit(count: number, unit: string, zeroText: string = '暂无'): string {
    if (count === 0) return zeroText;
    return `${count}${unit}`;
}

/**
 * 格式化时长单位
 * @param minutes 分钟数
 */
export function formatDurationUnit(minutes: number): string {
    if (minutes < 60) {
        return `${minutes}分钟`;
    } else if (minutes < 1440) {
        const hours = Math.floor(minutes / 60);
        const remainingMinutes = minutes % 60;
        if (remainingMinutes === 0) {
            return `${hours}小时`;
        } else {
            return `${hours}小时${remainingMinutes}分钟`;
        }
    } else {
        const days = Math.floor(minutes / 1440);
        const remainingHours = Math.floor((minutes % 1440) / 60);
        if (remainingHours === 0) {
            return `${days}天`;
        } else {
            return `${days}天${remainingHours}小时`;
        }
    }
}