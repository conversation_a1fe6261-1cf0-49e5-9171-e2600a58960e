<template>
  <view class="container">
    <!-- 1. 加载状态：由 useGigDetail Hook 自动管理 -->
    <view v-if="isLoading && !gigDetail" class="loading-container">
      <view class="loading-spinner"></view>
      <text class="loading-text">加载中...</text>
    </view>

    <!-- 2. 错误状态：由 useGigDetail Hook 自动管理 -->
    <view v-else-if="fetchError" class="error-container">
      <text class="error-text">{{
        fetchError.message || "加载失败，请稍后重试"
      }}</text>
      <button class="retry-button" @click="refreshGigDetail()">重试</button>
    </view>

    <!-- 3. 正常内容：当 gigDetail 数据存在时显示 -->
    <scroll-view
      v-else-if="gigDetail"
      scroll-y
      enable-flex
      class="detail-scroll-view"
      @refresherrefresh="onPullDownRefresh"
      :refresher-triggered="isLoading"
    >
      <!-- 主要信息卡片 -->
      <view class="main-info-card">
        <view class="header-section">
          <text class="job-type">{{ gigDetail.title }}</text>
          <view v-if="canApply" class="status-badge available">
            <text class="status-text">可报名</text>
          </view>
        </view>
        <text class="experience-text">{{ gigDetail.description }}</text>
        <view class="salary-section">
          <text class="salary-amount">{{
            formatGigSalary(gigDetail.salary, gigDetail.salary_unit)
          }}</text>
          <text class="salary-duration">{{
            formatWorkDuration(gigDetail)
          }}</text>
        </view>
        <view class="time-section">
          <text class="time-icon i-carbon-time"></text>
          <text class="time-text">{{
            formatTimeRange(gigDetail.start_time, gigDetail.end_time)
          }}</text>
        </view>
        <view class="location-section">
          <text class="location-icon i-carbon-location"></text>
          <text class="location-text">{{ gigDetail.address_name }}</text>
          <text class="distance-text">·{{ gigDetail.distance }}km</text>
        </view>
      </view>

      <!-- ... 其他卡片，如任务说明、发布者信息等，都使用 gigDetail ... -->
      <view class="publisher-card">
        <view class="publisher-info">
          <image
            :src="
              gigDetail.publisher?.avatar || '/static/images/default-avatar.png'
            "
            class="publisher-avatar"
          />
          <view class="publisher-details">
            <view class="publisher-name-section">
              <text class="publisher-name">{{
                gigDetail.publisher?.nickname || "匿名用户"
              }}</text>
            </view>
          </view>
        </view>
        <view class="contact-section" @tap="handleCallPhone">
          <text class="contact-icon i-carbon-phone"></text>
          <text class="contact-text">联系电话</text>
          <text class="phone-number">{{
            formatPhoneNumber(gigDetail.contact_phone)
          }}</text>
        </view>
      </view>
    </scroll-view>

    <!-- 4. 底部操作栏 -->
    <view v-if="gigDetail" class="bottom-action-bar safe-area-inset-bottom">
      <template v-if="gigDetail.user_id === userStore.user?.id">
        <!-- 发布者按钮 -->
      </template>
      <template v-else>
        <!-- 申请按钮：加载和禁用状态由 useApplyForGig 和 useGigDetail 共同管理 -->
        <button
          class="apply-button"
          @click="handleApply"
          :disabled="!canApply || isApplying"
          :class="{ 'disabled-button': !canApply || isApplying }"
        >
          <text class="button-text">{{
            isApplying
              ? "报名中..."
              : gigDetail?.has_applied
              ? "已报名"
              : "立即报名"
          }}</text>
        </button>
      </template>
    </view>
  </view>
</template>

<script setup lang="ts">
import { ref } from "vue";
import { onLoad, onPullDownRefresh } from "@dcloudio/uni-app";
import { useGigDetail, useApplyForGig } from "@/services/gig";
import { useUserStore } from "@/stores";
import { formatGigSalary } from "@/utils/business/gig";
import { formatTimeRange } from "@/utils/core/date";
import { showConfirm, formatPhoneNumber, navigateTo, showToast } from "@/utils";
import type { Gig } from "@/types/gig";

const userStore = useUserStore();

// 响应式地存储从 URL 获取的 gigId
const gigId = ref<number>(0);

// =================================================================
// 1. 调用核心业务 Hook，传入 gigId
//    所有数据 (gigDetail)、状态 (isLoading) 和业务判断 (canApply) 都从此获取
// =================================================================
const { gigDetail, isLoading, fetchError, canApply, refreshGigDetail } =
  useGigDetail(gigId);

// =================================================================
// 2. 调用申请零工的 Hook
//    它封装了申请动作的加载状态和执行函数
// =================================================================
const { isApplying, apply: performApply } = useApplyForGig();

// =================================================================
// 3. 生命周期钩子
// =================================================================
onLoad((options) => {
  if (options && options.id) {
    gigId.value = parseInt(options.id, 10);
    console.log(gigId.value);
  } else {
    showToast("无效的零工ID");
  }
});

// 下拉刷新逻辑
onPullDownRefresh(async () => {
  await refreshGigDetail(); // 调用 hook 返回的刷新函数
  uni.stopPullDownRefresh();
});

// =================================================================
// 4. 页面方法 (Methods)
//    这些方法现在非常简洁，只负责用户交互和调用 Hook 返回的函数
// =================================================================

/**
 * 处理报名申请
 */
async function handleApply() {
  if (!canApply.value || isApplying.value) return;

  const { user } = userStore;
  if (!user?.nickname || !user?.phone) {
    const confirmed = await showConfirm({
      title: "提示",
      content: "请先在'我的'页面完善您的姓名和联系方式再进行报名。",
      confirmText: "去完善",
    });
    if (confirmed) navigateTo("/pages/mine/profile");
    return;
  }

  const confirmed = await showConfirm({
    title: "确认报名",
    content: `您确定要报名【${gigDetail.value?.title}】吗？`,
    confirmText: "确认报名",
  });

  if (confirmed) {
    try {
      await performApply({
        gig_id: gigId.value,
        message: "我对此很感兴趣，希望获得这个机会！",
        applicant_name: user.nickname,
        applicant_phone: user.phone,
      });
      // 申请成功后，重新刷新详情数据以更新状态（例如“已报名”）
      refreshGigDetail();
    } catch (error) {
      // 错误已在全局拦截器中提示，这里可以不处理或添加特定逻辑
      console.error("申请失败:", error);
    }
  }
}

/**
 * 拨打电话
 */
function handleCallPhone() {
  const phone = gigDetail.value?.contact_phone;
  if (!phone) {
    uni.showToast({ title: "暂无联系方式", icon: "none" });
    return;
  }
  uni.makePhoneCall({ phoneNumber: phone });
}

/**
 * 格式化工作时长和总薪资
 */
function formatWorkDuration(gig: Gig | null): string {
  if (!gig) return "";
  const duration = gig.work_duration || 180;
  const salary = gig.salary || 0;
  const hours = Math.round(duration / 60);
  const total = salary * hours;
  return `${hours}小时·共¥${total}`;
}

// ... 其他页面方法，如 handleEditGig, goToPublisherProfile 等
</script>

<style lang="scss" scoped>
/* 样式保持不变 */
.container {
  min-height: 100vh;
  background: var(--bg-page);
  display: flex;
  flex-direction: column;
  box-sizing: border-box;
  width: 100%;
  max-width: 100vw;
  overflow-x: hidden;
}

.detail-scroll-view {
  flex: 1;
  margin: 20rpx;
  margin-bottom: 140rpx; /* 为底部操作栏留出空间 */
  box-sizing: border-box;
  height: calc(100vh - 140rpx); /* 确保滚动区域高度正确 */
}

.main-info-card {
  background: var(--bg-card);
  border-radius: 24rpx;
  padding: 32rpx;
  margin-bottom: 24rpx;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.06);
  width: 100%;
  box-sizing: border-box;
}

.header-section {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16rpx;
}

.job-type {
  font-size: 36rpx;
  font-weight: 700;
  color: var(--text-base);
}

.status-badge.available {
  background: var(--text-green);
  color: white;
  padding: 8rpx 16rpx;
  border-radius: 20rpx;
  font-size: 24rpx;
  font-weight: 600;
}

.status-badge.available .status-text {
  color: white;
}

.experience-text {
  font-size: 28rpx;
  color: var(--text-secondary);
  line-height: 1.5;
  margin-bottom: 24rpx;
  word-wrap: break-word;
  word-break: break-all;
}

.salary-section {
  display: flex;
  align-items: baseline;
  gap: 12rpx;
  margin-bottom: 24rpx;
}

.salary-amount {
  font-size: 48rpx;
  font-weight: 700;
  color: var(--text-red);
}

.salary-duration {
  font-size: 28rpx;
  color: var(--text-secondary);
}

.time-section,
.location-section {
  display: flex;
  align-items: center;
  gap: 12rpx;
  margin-bottom: 16rpx;
}

.time-icon,
.location-icon {
  font-size: 32rpx;
  color: var(--text-info);
}

.time-text,
.location-text {
  font-size: 28rpx;
  color: var(--text-base);
  word-wrap: break-word;
  flex: 1;
}

.distance-text {
  font-size: 28rpx;
  color: var(--text-info);
}

.location-section {
  margin-bottom: 0;
}

.task-description-card,
.checkin-card,
.publisher-card {
  background: var(--bg-card);
  border-radius: 24rpx;
  padding: 32rpx;
  margin-bottom: 24rpx;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.06);
  width: 100%;
  box-sizing: border-box;
}

.card-title {
  font-size: 32rpx;
  font-weight: 600;
  color: var(--text-base);
  margin-bottom: 24rpx;
}

.task-list .task-item {
  display: flex;
  align-items: flex-start;
  gap: 12rpx;
  margin-bottom: 16rpx;
}

.task-list .task-item:last-child {
  margin-bottom: 0;
}

.task-number {
  font-size: 28rpx;
  color: var(--text-secondary);
  font-weight: 600;
  flex-shrink: 0;
  margin-top: 2rpx;
}

.task-content {
  font-size: 28rpx;
  color: var(--text-base);
  line-height: 1.5;
  flex: 1;
  word-wrap: break-word;
  word-break: break-all;
}

.publisher-info {
  display: flex;
  align-items: center;
  gap: 24rpx;
  margin-bottom: 24rpx;
}

.publisher-avatar {
  width: 96rpx;
  height: 96rpx;
  border-radius: 50%;
  background: var(--bg-tag);
}

.publisher-details {
  flex: 1;
}

.publisher-name-section {
  display: flex;
  align-items: center;
  gap: 12rpx;
  margin-bottom: 8rpx;
}

.publisher-name {
  font-size: 32rpx;
  font-weight: 600;
  color: var(--text-base);
  word-wrap: break-word;
  flex: 1;
}

.verified-badge {
  display: flex;
  align-items: center;
  gap: 4rpx;
  background: var(--text-blue);
  color: white;
  padding: 4rpx 12rpx;
  border-radius: 16rpx;
}

.verified-icon {
  font-size: 20rpx;
  color: white;
}

.verified-text {
  font-size: 20rpx;
  color: white;
}

.publisher-stats {
  font-size: 24rpx;
  color: var(--text-info);
  word-wrap: break-word;
}

.contact-section {
  display: flex;
  align-items: center;
  gap: 12rpx;
  padding: 24rpx;
  background: var(--bg-secondary);
  border-radius: 16rpx;
}

.contact-icon {
  font-size: 32rpx;
  color: var(--text-blue);
}

.contact-text {
  font-size: 28rpx;
  color: var(--text-base);
  margin-right: auto;
  word-wrap: break-word;
}

.phone-number {
  font-size: 28rpx;
  color: var(--text-secondary);
  word-wrap: break-word;
}

.bottom-action-bar {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  background: var(--bg-card);
  padding: 24rpx 32rpx;
  border-top: 1rpx solid var(--border-color);
  box-shadow: 0 -4rpx 20rpx rgba(0, 0, 0, 0.06);
  display: flex;
  gap: 24rpx;
  z-index: 999;
}

.apply-button {
  flex: 1;
  height: 88rpx;
  background: #4a90e2;
  color: white;
  border: none;
  border-radius: 44rpx;
  font-size: 32rpx;
  font-weight: 600;
  display: flex;
  align-items: center;
  justify-content: center;
}

.apply-button.disabled-button {
  background: var(--bg-disabled);
  color: var(--text-disabled);
}

.apply-button .button-text {
  color: inherit;
}

.loading-container,
.error-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 60vh;
  gap: 24rpx;
}

.loading-spinner {
  width: 60rpx;
  height: 60rpx;
  border: 4rpx solid var(--border-color);
  border-top: 4rpx solid var(--primary);
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

.loading-text,
.error-text {
  font-size: 28rpx;
  color: var(--text-secondary);
}

.retry-button {
  background: var(--primary);
  color: white;
  border: none;
  padding: 16rpx 32rpx;
  border-radius: 24rpx;
  font-size: 28rpx;
}

@keyframes spin {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}
</style>
