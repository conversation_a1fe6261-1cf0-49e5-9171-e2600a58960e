<template>
  <view class="applicants-page">
    <CustomNavBar title="报名管理" />

    <!-- 1. 加载状态由 Hook 管理 -->
    <view v-if="isLoading" class="loading-container">
      <view class="loading-spinner"></view>
      <text class="loading-text">正在加载申请信息...</text>
    </view>

    <!-- 2. 错误状态由 Hook 管理 -->
    <view v-else-if="fetchError" class="error-container">
      <text class="error-text">{{ fetchError.message || "加载失败" }}</text>
      <button class="retry-button" @click="refresh">重试</button>
    </view>

    <!-- 3. 内容区域 -->
    <view v-else class="content-container">
      <!-- 申请者列表 -->
      <view
        v-if="applicantsList && applicantsList.length > 0"
        class="applicants-list"
      >
        <view
          v-for="applicant in applicantsList"
          :key="applicant.id"
          class="applicant-card"
          @tap="goToApplicantProfile(applicant.user_id)"
        >
          <!-- ... 卡片内容保持不变 ... -->
          <view class="applicant-header">
            <view class="applicant-info">
              <image
                :src="applicant.applicant_info?.avatar || defaultAvatar"
                class="applicant-avatar"
              />
              <view class="applicant-details">
                <text class="applicant-name">{{
                  applicant.applicant_info?.nickname || "匿名用户"
                }}</text>
                <text class="apply-time">{{
                  formatDate(applicant.created_at, DateFormat.DATE_SIMPLE)
                }}</text>
              </view>
            </view>
            <view
              class="status-badge"
              :class="getStatusDetails(applicant.status).class"
            >
              <text class="status-text">{{
                getStatusDetails(applicant.status).text
              }}</text>
            </view>
          </view>
          <view class="contact-info">
            <view class="contact-item">
              <text class="contact-icon i-carbon-phone"></text>
              <text class="contact-text">{{ applicant.applicant_phone }}</text>
            </view>
          </view>
          <view v-if="applicant.message" class="application-message">
            <text class="message-label">申请留言：</text>
            <text class="message-content">{{ applicant.message }}</text>
          </view>

          <!-- 4. 操作按钮区域，状态由 Hook 管理 -->
          <view class="action-buttons">
            <template v-if="applicant.status === GigApplicationStatus.Pending">
              <button
                class="action-btn reject-btn"
                :disabled="isReviewing"
                @click.stop="
                  handleUpdateStatus(
                    applicant.id,
                    GigApplicationStatus.Rejected
                  )
                "
              >
                <text class="btn-text">拒绝</text>
              </button>
              <button
                class="action-btn approve-btn"
                :disabled="isReviewing"
                @click.stop="
                  handleUpdateStatus(
                    applicant.id,
                    GigApplicationStatus.Confirmed
                  )
                "
              >
                <text class="btn-text">{{
                  isReviewing ? "处理中..." : "录用"
                }}</text>
              </button>
            </template>
            <template v-else>
              <button
                class="action-btn contact-btn"
                @click.stop="contactApplicant(applicant)"
              >
                <text class="btn-text">联系Ta</text>
              </button>
            </template>
          </view>
        </view>
      </view>

      <!-- 空状态页面 -->
      <view v-else class="empty-state">
        <!-- ... 空状态内容保持不变 ... -->
      </view>
    </view>
  </view>
</template>

<script setup lang="ts">
import { ref, computed } from "vue";
import { onLoad } from "@dcloudio/uni-app";
import { useGigApplicants } from "@/services/gig";
import type { GigApplication } from "@/types/gig";
import { GigApplicationStatus } from "@/constants/gig";
import { formatDate, DateFormat } from "@/utils/core/date";
import { getGigApplicationStatusDetails } from "@/utils/business/gig";
import { showToast } from "@/utils";
import defaultAvatar from "@/static/images/default-avatar.png";

const gigId = ref<number>(0);

// 1. 调用封装好的 Hook，传入 gigId
const { applicants, isLoading, fetchError, isReviewing, review, refresh } =
  useGigApplicants(gigId);

// 2. 正确解构applicants数据
const applicantsList = computed(() => {
  return applicants.value?.list || [];
});

onLoad((options) => {
  if (options && options.gigId) {
    gigId.value = parseInt(options.gigId, 10);
  } else {
    showToast("无效的零工ID");
  }
});

const getStatusDetails = (status: GigApplicationStatus) => {
  return getGigApplicationStatusDetails(status, "recruiter");
};

// 2. 更新状态的方法现在调用 Hook 返回的 review 函数
const handleUpdateStatus = async (
  applicationId: number,
  newStatus: GigApplicationStatus
) => {
  await review({
    application_id: applicationId,
    new_status: newStatus,
  });
  // 成功后的刷新和提示逻辑已在 Hook 内部处理
};

const goToApplicantProfile = (userId?: number) => {
  if (!userId) return;
  console.log(`跳转到用户详情页: ${userId}`);
};

const contactApplicant = (applicant: GigApplication) => {
  if (!applicant.applicant_phone) {
    showToast("暂无联系方式");
    return;
  }
  uni.makePhoneCall({ phoneNumber: applicant.applicant_phone });
};
</script>

<style lang="scss" scoped>
/* 样式保持不变 */
.applicants-page {
  min-height: 100vh;
  background: var(--bg-page);
}

.loading-container,
.error-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  min-height: 60vh;
  padding: 80rpx 40rpx;
}

.loading-spinner {
  width: 80rpx;
  height: 80rpx;
  border: 6rpx solid var(--bg-tag);
  border-top: 6rpx solid var(--primary);
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 32rpx;
}

.loading-text,
.error-text {
  font-size: 30rpx;
  color: var(--text-secondary);
}

.retry-button {
  background: var(--primary);
  color: white;
  border: none;
  padding: 16rpx 32rpx;
  border-radius: 24rpx;
  font-size: 28rpx;
  margin-top: 24rpx;
}

@keyframes spin {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}

.content-container {
  padding: 24rpx;
}

.applicant-card {
  background: var(--bg-card);
  border-radius: 24rpx;
  margin-bottom: 24rpx;
  padding: 32rpx;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.06);
}

.applicant-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24rpx;
}

.applicant-info {
  display: flex;
  align-items: center;
  flex: 1;
}

.applicant-avatar {
  width: 80rpx;
  height: 80rpx;
  border-radius: 50%;
  margin-right: 20rpx;
  background: var(--bg-tag);
}

.applicant-details {
  flex: 1;
}

.applicant-name {
  font-size: 32rpx;
  font-weight: 600;
  color: var(--text-base);
  margin-bottom: 4rpx;
  display: block;
}

.apply-time {
  font-size: 24rpx;
  color: var(--text-info);
}

.status-badge {
  padding: 8rpx 16rpx;
  border-radius: 20rpx;
  font-size: 24rpx;
  font-weight: 600;
}

.status-badge .status-text {
  color: inherit;
}
.status-badge.status-pending {
  background: var(--bg-warning-light);
  color: var(--text-yellow);
}
.status-badge.status-confirmed {
  background: var(--bg-success-light);
  color: var(--text-green);
}
.status-badge.status-rejected {
  background: var(--bg-danger-light);
  color: var(--text-red);
}
.status-badge.status-withdrawn {
  background: var(--bg-tag);
  color: var(--text-info);
}

.contact-info {
  margin-bottom: 24rpx;
}
.contact-item {
  display: flex;
  align-items: center;
  gap: 12rpx;
}
.contact-icon {
  font-size: 28rpx;
  color: var(--primary);
}
.contact-text {
  font-size: 28rpx;
  color: var(--text-base);
  font-weight: 500;
}

.application-message {
  background: var(--bg-tag);
  border-radius: 16rpx;
  padding: 20rpx;
  margin-bottom: 24rpx;
}

.message-label {
  font-size: 24rpx;
  color: var(--text-info);
  margin-bottom: 8rpx;
  display: block;
}

.message-content {
  font-size: 28rpx;
  color: var(--text-base);
  line-height: 1.5;
}

.action-buttons {
  display: flex;
  justify-content: flex-end;
  gap: 16rpx;
}

.action-btn {
  border: none;
  border-radius: 32rpx;
  padding: 16rpx 32rpx;
  font-size: 26rpx;
  font-weight: 600;
  transition: all 0.3s ease;
}

.action-btn .btn-text {
  color: inherit;
}

.reject-btn {
  background: var(--bg-danger-light);
  color: var(--text-red);
}
.reject-btn:active {
  background: var(--text-red);
}
.reject-btn:active .btn-text {
  color: white;
}

.approve-btn {
  background: linear-gradient(135deg, var(--text-green), #4ade80);
  color: white;
  box-shadow: 0 4rpx 16rpx rgba(34, 197, 94, 0.3);
}
.approve-btn:active {
  transform: scale(0.98);
  box-shadow: 0 2rpx 8rpx rgba(34, 197, 94, 0.4);
}

.contact-btn {
  background: var(--bg-primary-light);
  color: var(--primary);
}
.contact-btn:active {
  background: var(--primary);
}
.contact-btn:active .btn-text {
  color: white;
}

.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  min-height: 60vh;
  padding: 80rpx 40rpx;
  text-align: center;
}

.empty-illustration {
  margin-bottom: 40rpx;
}
.empty-icon {
  font-size: 160rpx;
  color: var(--text-info);
  opacity: 0.6;
}
.empty-content {
  margin-bottom: 48rpx;
}
.empty-title {
  font-size: 36rpx;
  font-weight: 600;
  color: var(--text-base);
  margin-bottom: 16rpx;
  display: block;
}
.empty-desc {
  font-size: 28rpx;
  color: var(--text-secondary);
  line-height: 1.5;
  max-width: 500rpx;
}

.share-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 12rpx;
  background: linear-gradient(135deg, var(--primary), #ff8533);
  color: white;
  border: none;
  border-radius: 44rpx;
  padding: 20rpx 40rpx;
  font-size: 28rpx;
  font-weight: 600;
  box-shadow: 0 8rpx 24rpx rgba(255, 109, 0, 0.3);
}

.share-btn:active {
  transform: translateY(2rpx);
  box-shadow: 0 4rpx 12rpx rgba(255, 109, 0, 0.4);
}
.share-icon {
  font-size: 32rpx;
  color: white;
}
.share-text {
  color: white;
}
</style>
