<template>
  <view class="gig-recruiter-page">
    <uni-nav-bar title="招聘中心" :fixed="true" status-bar :border="false" />

    <!-- 头部欢迎区域 -->
    <view class="welcome-section">
      <view class="welcome-content">
        <text class="welcome-title">欢迎回来，招聘官！</text>
        <text class="welcome-subtitle">管理您的招聘需求，找到最合适的人才</text>
      </view>
      <view class="role-switch" @tap="switchToSeeker">
        <text class="switch-text">求职模式</text>
        <text class="i-carbon-arrow-right switch-icon"></text>
      </view>
    </view>

    <!-- 快捷功能入口 -->
    <view class="quick-actions">
      <view class="action-item" @tap="goToPublish">
        <view class="action-icon publish-icon">
          <text class="i-carbon-add-alt"></text>
        </view>
        <text class="action-text">发布零工</text>
      </view>
      <view class="action-item" @tap="viewAllPublished">
        <view class="action-icon manage-icon">
          <text class="i-carbon-task"></text>
        </view>
        <text class="action-text">管理发布</text>
      </view>
      <view class="action-item" @tap="viewCalendar">
        <view class="action-icon calendar-icon">
          <text class="i-carbon-calendar"></text>
        </view>
        <text class="action-text">工作日历</text>
      </view>
      <view class="action-item" @tap="viewStats">
        <view class="action-icon stats-icon">
          <text class="i-carbon-analytics"></text>
        </view>
        <text class="action-text">数据统计</text>
      </view>
    </view>

    <!-- 我的发布列表 -->
    <view class="published-section">
      <view class="section-header">
        <text class="section-title">我的发布</text>
        <view class="section-action" @tap="viewAllPublished">
          <text class="action-text">查看全部</text>
        </view>
      </view>

      <view v-if="isLoadingGigs" class="loading-state">加载中...</view>
      <view v-else-if="fetchGigsError" class="error-state">{{ fetchGigsError.message }}</view>
      <view v-else-if="recentPublished.length > 0" class="published-list">
        <view v-for="gig in recentPublished" :key="gig.id" @tap="navigateToDetail(gig.id)">
          <GigCard :gig="gig" />
        </view>
      </view>
      <view v-else class="empty-published">暂无发布</view>
    </view>

    <!-- 最新报名 -->
    <view class="applications-section">
      <view class="section-header">
        <text class="section-title">最新报名</text>
        <view class="section-action" @tap="viewAllApplications">
          <text class="action-text">查看全部</text>
        </view>
      </view>

      <view v-if="isLoadingApps" class="loading-state">加载中...</view>
      <view v-else-if="fetchAppsError" class="error-state">{{ fetchAppsError.message }}</view>
      <view v-else-if="recentApplications.length > 0" class="applications-list">
        <view v-for="app in recentApplications" :key="app.id" class="application-item">
          <text>{{ app.applicant_info?.nickname }} 报名了 {{ app.gig_info?.title }}</text>
          <view class="app-actions">
            <button @tap.stop="quickAction(app, GigApplicationStatus.Confirmed)" :disabled="isReviewing">通过</button>
            <button @tap.stop="quickAction(app, GigApplicationStatus.Rejected)" :disabled="isReviewing">拒绝</button>
          </view>
        </view>
      </view>
      <view v-else class="empty-applications">暂无报名</view>
    </view>
  </view>
</template>

<script setup lang="ts">
import { onShow } from "@dcloudio/uni-app";
import GigCard from "./components/GigCard.vue";
import { useRecruiterDashboard } from "@/services/gig";
import { GigApplicationStatus } from "@/constants/gig";
import type { GigApplication } from "@/types/gig";
import { navigateTo, buildUrl } from "@/utils";
import { useGlobalStore } from "@/stores/global";

// 1. 调用 Hook
const {
  recentPublished,
  isLoadingGigs,
  fetchGigsError,
  refreshGigs,
  recentApplications,
  isLoadingApps,
  fetchAppsError,
  refreshApps,
  isReviewing,
  review,
} = useRecruiterDashboard();

// 2. 页面显示时刷新数据
onShow(() => {
  refreshGigs();
  refreshApps();
});

// 3. 快速审批
const quickAction = async (app: GigApplication, newStatus: GigApplicationStatus) => {
  await review({ application_id: app.id, new_status: newStatus });
};

// 导航方法
const navigateToDetail = (id: number) => navigateTo(buildUrl("/pages/gig/detail", { id }));
const viewAllPublished = () => navigateTo("/pages/gig/manage");
const viewAllApplications = () => navigateTo("/pages/gig/manage");
const goToPublish = () => navigateTo("/pages/gig/publish");
const viewCalendar = () => navigateTo("/pages/gig/calendar");
const viewStats = () => navigateTo("/pages/gig/stats");

const switchToSeeker = () => {
  const globalStore = useGlobalStore();
  globalStore.setGigRole("seeker");
  navigateTo({ url: "/pages/gig/seeker", openType: "redirect" });
};

</script>

<style scoped lang="scss">
.gig-recruiter-page {
  min-height: 100vh;
  background: linear-gradient(135deg, var(--primary-50), var(--bg-page));
  padding-bottom: var(--spacing-40);
}

.welcome-section {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: var(--spacing-24) var(--spacing-16);
  margin-top: var(--spacing-16);
  background: var(--bg-card);
  margin: var(--spacing-16);
  border-radius: var(--radius-lg);
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.06);
}

.welcome-content {
  flex: 1;
}

.welcome-title {
  display: block;
  font-size: var(--font-size-xl);
  font-weight: var(--font-weight-bold);
  color: var(--text-base);
  margin-bottom: var(--spacing-4);
}

.welcome-subtitle {
  font-size: var(--font-size-sm);
  color: var(--text-secondary);
  line-height: 1.4;
}

.role-switch {
  display: flex;
  align-items: center;
  gap: var(--spacing-6);
  padding: var(--spacing-8) var(--spacing-12);
  background: var(--bg-primary-light);
  color: var(--primary);
  border-radius: var(--radius);
  font-size: var(--font-size-sm);
  font-weight: var(--font-weight-medium);
  transition: all 0.3s ease;
  
  &:active {
    transform: scale(0.98);
    background: var(--primary);
    color: var(--text-inverse);
  }
}

.switch-text {
  color: inherit;
}

.switch-icon {
  font-size: var(--font-size-base);
  color: inherit;
}

.quick-actions {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: var(--spacing-12);
  padding: 0 var(--spacing-16);
  margin-bottom: var(--spacing-24);
}

.action-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: var(--spacing-20);
  background: var(--bg-card);
  border-radius: var(--radius-lg);
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.06);
  transition: all 0.3s ease;
  
  &:active {
    transform: scale(0.98);
    box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.1);
  }
}

.action-icon {
  width: 80rpx;
  height: 80rpx;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: var(--spacing-12);
  
  text {
    font-size: 40rpx;
    color: var(--text-inverse);
  }
  
  &.publish-icon {
    background: linear-gradient(135deg, var(--primary), #ff8533);
    box-shadow: 0 8rpx 24rpx rgba(255, 109, 0, 0.3);
  }
  
  &.manage-icon {
    background: linear-gradient(135deg, var(--text-blue), #60a5fa);
    box-shadow: 0 8rpx 24rpx rgba(59, 130, 246, 0.3);
  }
  
  &.calendar-icon {
    background: linear-gradient(135deg, var(--text-green), #4ade80);
    box-shadow: 0 8rpx 24rpx rgba(34, 197, 94, 0.3);
  }
  
  &.stats-icon {
    background: linear-gradient(135deg, var(--text-purple), #a78bfa);
    box-shadow: 0 8rpx 24rpx rgba(139, 92, 246, 0.3);
  }
}

.action-text {
  font-size: var(--font-size-base);
  font-weight: var(--font-weight-medium);
  color: var(--text-base);
}

.published-section,
.applications-section {
  margin: 0 var(--spacing-16) var(--spacing-24);
  background: var(--bg-card);
  border-radius: var(--radius-lg);
  padding: var(--spacing-20);
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.06);
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: var(--spacing-16);
}

.section-title {
  font-size: var(--font-size-lg);
  font-weight: var(--font-weight-bold);
  color: var(--text-base);
}

.section-action {
  display: flex;
  align-items: center;
  gap: var(--spacing-4);
  padding: var(--spacing-6) var(--spacing-12);
  background: var(--bg-tag);
  border-radius: var(--radius);
  transition: all 0.3s ease;
  
  &:active {
    background: var(--primary);
    transform: scale(0.98);
    
    .action-text {
      color: var(--text-inverse);
    }
  }
}

.action-text {
  font-size: var(--font-size-sm);
  color: var(--text-secondary);
  font-weight: var(--font-weight-medium);
}

.loading-state,
.error-state {
  text-align: center;
  padding: var(--spacing-32);
  color: var(--text-info);
  font-size: var(--font-size-base);
}

.error-state {
  color: var(--text-red);
}

.published-list,
.applications-list {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-12);
}

.application-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: var(--spacing-16);
  background: var(--bg-tag);
  border-radius: var(--radius);
  
  text {
    flex: 1;
    font-size: var(--font-size-sm);
    color: var(--text-base);
    line-height: 1.4;
  }
}

.app-actions {
  display: flex;
  gap: var(--spacing-8);
  margin-left: var(--spacing-12);
  
  button {
    padding: var(--spacing-6) var(--spacing-12);
    border: none;
    border-radius: var(--radius-sm);
    font-size: var(--font-size-xs);
    font-weight: var(--font-weight-medium);
    transition: all 0.3s ease;
    
    &:first-child {
      background: var(--bg-danger-light);
      color: var(--text-red);
      
      &:active {
        background: var(--text-red);
        color: var(--text-inverse);
      }
    }
    
    &:last-child {
      background: var(--bg-success-light);
      color: var(--text-green);
      
      &:active {
        background: var(--text-green);
        color: var(--text-inverse);
      }
    }
    
    &:disabled {
      opacity: 0.6;
      transform: none !important;
    }
  }
}

.empty-published,
.empty-applications {
  text-align: center;
  padding: var(--spacing-40);
  color: var(--text-info);
  font-size: var(--font-size-base);
}

// 响应式适配
@media (max-width: 750rpx) {
  .welcome-section {
    flex-direction: column;
    gap: var(--spacing-16);
    text-align: center;
  }
  
  .quick-actions {
    grid-template-columns: repeat(4, 1fr);
    gap: var(--spacing-8);
  }
  
  .action-item {
    padding: var(--spacing-16);
  }
  
  .action-icon {
    width: 60rpx;
    height: 60rpx;
    margin-bottom: var(--spacing-8);
    
    text {
      font-size: 32rpx;
    }
  }
  
  .action-text {
    font-size: var(--font-size-xs);
  }
}

// 加载动画
@keyframes pulse {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.7;
  }
}

.loading-state {
  animation: pulse 2s infinite;
}
</style>