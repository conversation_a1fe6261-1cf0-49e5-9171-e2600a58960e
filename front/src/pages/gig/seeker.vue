<template>
  <view class="gig-seeker-page bg-page min-h-screen">
    <!-- 使用z-paging实现高性能列表 -->
    <z-paging
      ref="pagingRef"
      v-model="gigList"
      :safe-area-inset-bottom="true"
      :refresher-enabled="true"
      :auto="true"
      :default-page-size="10"
      @query="queryGigList"
      @scroll="onPageScroll"
    >
      <template #top>
        <uni-nav-bar
          title="零工广场"
          :fixed="true"
          status-bar
          :border="false"
        />
        <!-- 顶部搜索和切换按钮 -->
        <view class="header-bar bg-white">
          <view class="flex items-center justify-between px-30rpx py-16rpx">
            <view
              class="search-box flex-1 flex items-center bg-search rounded-full px-30rpx py-16rpx mr-20rpx"
            >
              <text class="i-carbon-search mr-10rpx text-info"></text>
              <input
                :value="searchKeyword"
                @input="
                  (e) => {
                    searchKeyword = e.detail.value;
                    generateSearchSuggestions(e.detail.value);
                  }
                "
                class="flex-1 text-28rpx text-base bg-transparent"
                placeholder="搜索任务，如：派传单、搬家"
                placeholder-class="text-info"
                confirm-type="search"
                @confirm="handleSearch"
                @focus="isSearching = true"
              />
              <text
                v-if="searchKeyword"
                class="i-carbon-close-filled text-grey ml-10rpx"
                @tap="clearSearch"
              ></text>
            </view>
            <!-- 切换到招人模式按钮 -->
            <view
              class="mode-switch bg-primary-light rounded-full px-24rpx py-12rpx"
              @tap="switchToRecruiter"
            >
              <view class="flex items-center">
                <text
                  class="i-carbon-user-certification text-20rpx text-primary mr-8rpx"
                ></text>
                <text class="text-primary font-semibold text-26rpx"
                  >我要招人</text
                >
              </view>
            </view>
          </view>

          <!-- 搜索建议和历史 -->
          <view v-if="isSearching" class="search-panel">
            <!-- 搜索建议 -->
            <view
              v-if="searchSuggestions.length > 0"
              class="search-suggestions"
            >
              <view
                v-for="(suggestion, index) in searchSuggestions"
                :key="'suggestion-' + index"
                class="suggestion-item"
                @tap="selectSuggestion(suggestion)"
              >
                <text class="i-carbon-search text-grey mr-16rpx"></text>
                <text>{{ suggestion }}</text>
              </view>
            </view>

            <!-- 搜索历史 -->
            <view v-else-if="searchHistory.length > 0" class="search-history">
              <view class="history-header">
                <text class="history-title">搜索历史</text>
                <text class="clear-history" @tap="clearSearchHistory"
                  >清空</text
                >
              </view>
              <view class="history-list">
                <view
                  v-for="(history, index) in searchHistory"
                  :key="'history-' + index"
                  class="history-item"
                  @tap="selectHistory(history)"
                >
                  <text class="i-carbon-time text-grey mr-16rpx"></text>
                  <text>{{ history }}</text>
                </view>
              </view>
            </view>

            <!-- 无搜索历史提示 -->
            <view v-else class="no-history">
              <text class="no-history-text">暂无搜索历史</text>
            </view>

            <!-- 关闭搜索面板按钮 -->
            <view class="close-search" @tap="isSearching = false">
              <text>关闭</text>
            </view>
          </view>
        </view>
      </template>

      <!-- 找零工功能入口 -->
      <view class="function-entrance bg-white px-20rpx py-30rpx">
        <view class="grid grid-cols-2 gap-20rpx">
          <view
            v-for="func in seekerFunctions"
            :key="func.id"
            class="function-item flex flex-col items-center justify-center py-20rpx"
            @tap="navigateToFunction(func.path)"
          >
            <view
              class="icon-wrapper w-88rpx h-88rpx rounded-2xl flex items-center justify-center mb-12rpx"
              :style="{
                background: func.gradient,
                boxShadow: `0 8rpx 24rpx ${func.shadowColor}`,
                transform: 'translateZ(0)',
              }"
            >
              <text
                :class="func.icon"
                class="text-44rpx text-white drop-shadow"
              ></text>
            </view>
            <text class="text-26rpx text-secondary font-medium">{{
              func.name
            }}</text>
          </view>
        </view>
      </view>

      <!-- 筛选排序 -->
      <view class="tabs-wrapper box-shadow sticky top-0 z-10">
        <tui-tabs
          :tabs="sortTabs"
          :currentTab="sortTabIndex"
          sliderBgColor="var(--primary)"
          selectedColor="var(--primary)"
          color="var(--text-secondary)"
          :bold="true"
          :height="80"
          :sliderWidth="48"
          :sliderHeight="6"
          backgroundColor="var(--bg-card)"
          @change="onTabChange"
          :isFixed="false"
        ></tui-tabs>
      </view>

      <!-- 零工列表 -->
      <view v-for="gig in gigList" :key="gig.id" class="gig-card-wrapper mb-4">
        <GigCard
          :gig="gig"
          mode="seeker"
          @apply="handleApply"
          @view-detail="handleViewDetail"
          @card-click="handleCardClick"
        />
      </view>
    </z-paging>
  </view>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, nextTick } from "vue";
import GigCard from "./components/GigCard.vue";
import tuiTabs from "@/components/thorui/tui-tabs/tui-tabs.vue";
import type { ListGigsRequest, Gig } from "@/types/gig";
import GigApi from "@/api/gig";
import { navigateTo, buildUrl } from "@/utils";
import { useGlobalStore } from "@/stores/global";

// z-paging引用
const pagingRef = ref<any>(null);

// z-paging的数据列表
const gigList = ref<Gig[]>([]);

// 搜索和筛选参数
const searchParams = ref<ListGigsRequest>({
  page: 1,
  page_size: 10,
  sort_by: "latest",
  keyword: "",
  user_lat: undefined,
  user_lon: undefined,
});

// 加载状态（用于其他UI反馈）
const isLoading = ref(false);

const currentSort = computed(() => searchParams.value.sort_by);

// 搜索关键词
const searchKeyword = ref("");
const isSearching = ref(false);
const searchHistory = ref<string[]>([]);
const searchSuggestions = ref<string[]>([]);

// 用户位置
const userLocation = ref<{ latitude: number; longitude: number } | null>(null);

// 排序选项卡数据 - 简化版本，只保留最新发布和附近零工
const sortTabs = ref([
  { name: "最新发布", value: "latest" },
  { name: "附近零工", value: "distance" },
]);

// 当前选中的选项卡索引
const sortTabIndex = computed(() => {
  return sortTabs.value.findIndex((tab) => tab.value === currentSort.value);
});

// 找零工角色的功能入口
const seekerFunctions = [
  {
    id: "my_applications",
    name: "我的报名",
    icon: "i-carbon-document-signed",
    path: "/pages/gig/my-applications",
    gradient: "linear-gradient(135deg, #667eea 0%, #764ba2 100%)",
    shadowColor: "rgba(102, 126, 234, 0.3)",
  },
  {
    id: "work_calendar",
    name: "干活日历",
    icon: "i-carbon-calendar",
    path: "/pages/gig/calendar",
    gradient: "linear-gradient(135deg, #f093fb 0%, #f5576c 100%)",
    shadowColor: "rgba(240, 147, 251, 0.3)",
  },
];

// --- 方法 ---

/**
 * 获取用户位置
 */
const getUserLocation = async () => {
  try {
    const res = await uni.getLocation({ type: "gcj02" });
    userLocation.value = {
      latitude: res.latitude,
      longitude: res.longitude,
    };
    // 更新服务层的位置信息
    setUserLocation(res.latitude, res.longitude);
  } catch (err) {
    console.warn("获取位置失败:", err);
    // 可选: 给用户提示
  }
};

// z-paging查询数据方法 - 标准z-paging模式
const queryGigList = async (pageNo: number, pageSize: number) => {
  try {

    // 构建请求参数
    const params: ListGigsRequest = {
      ...searchParams.value,
      page: pageNo,
      page_size: pageSize,
      keyword: searchKeyword.value,
    };

    // 直接调用API获取数据
    const res = await GigApi.list(params);
    // 将结果传递给z-paging
    pagingRef.value.complete(res.list);
  } catch (error) {
    console.error("获取零工列表失败:", error);
    pagingRef.value.complete(false);
  }
};

// 页面滚动时关闭搜索面板
const onPageScroll = () => {
  if (isSearching.value) {
    isSearching.value = false;
  }
};

// 处理搜索输入 - 生成建议
const generateSearchSuggestions = (value: string) => {
  if (!value) {
    searchSuggestions.value = [];
    return;
  }

  setTimeout(() => {
    searchSuggestions.value = [
      `${value}相关工作`,
      `${value}兼职`,
      `${value}全职`,
      `${value}日结`,
    ].slice(0, 3);
  }, 300);
};

// 处理搜索确认
const handleSearch = () => {
  isSearching.value = false;
  // 更新搜索参数
  searchParams.value.keyword = searchKeyword.value;
  searchParams.value.page = 1; // 重置页码
  // 刷新z-paging
  pagingRef.value.reload();
};

// 清空搜索
const clearSearch = () => {
  searchKeyword.value = "";
  searchParams.value.keyword = "";
  searchParams.value.page = 1;
  pagingRef.value.reload();
};

// 选择搜索建议
const selectSuggestion = (suggestion: string) => {
  searchKeyword.value = suggestion;
  handleSearch();
};

// 选择搜索历史
const selectHistory = (history: string) => {
  searchKeyword.value = history;
  handleSearch();
};

// 清空搜索历史
const clearSearchHistory = () => {
  searchHistory.value = [];
  try {
    uni.removeStorageSync("gigSearchHistory");
  } catch (e) {
    console.error("清空搜索历史失败", e);
  }
};

// 切换到招人模式
const switchToRecruiter = () => {
  const globalStore = useGlobalStore();
  globalStore.setGigRole("recruiter");
  navigateTo({ url: "/pages/gig/recruiter", openType: "redirect" });
};

/**
 * 切换排序选项卡
 * @param e - tui-tabs返回的事件对象
 */
const onTabChange = (e: { index: number }) => {
  const newSort = sortTabs.value[e.index].value as ListGigsRequest["sort_by"];
  searchParams.value.sort_by = newSort;
  searchParams.value.page = 1; // 重置页码
  pagingRef.value.reload(); // 重新加载列表
};

// 设置用户位置
const setUserLocation = (latitude: number, longitude: number) => {
  searchParams.value.user_lat = latitude;
  searchParams.value.user_lon = longitude;
};

const navigateToFunction = (path: string) => {
  navigateTo(path);
};

const navigateToDetail = (id: string | number) => {
  navigateTo(buildUrl("/pages/gig/detail", { id }));
};

// GigCard事件处理
const handleApply = (gig: Gig) => {
  // 直接跳转到详情页面进行申请
  navigateToDetail(gig.id);
};

const handleViewDetail = (gig: Gig) => {
  navigateToDetail(gig.id);
};

const handleCardClick = (gig: Gig) => {
  navigateToDetail(gig.id);
};

onMounted(async () => {
  // 加载搜索历史
  try {
    const historyStr = uni.getStorageSync("gigSearchHistory");
    if (historyStr) {
      searchHistory.value = JSON.parse(historyStr);
    }
  } catch (e) {
    console.error("加载搜索历史失败", e);
  }
  // 组件挂载时自动获取位置
  getUserLocation();
});
</script>

<style lang="scss" scoped>
.gig-seeker-page {
  min-height: 100vh;
  background-color: var(--bg-page);
}

.header-bar {
  background-color: var(--bg-card);
  border-bottom: 1rpx solid var(--border-color);
  box-shadow: var(--shadow-sm);
}

.search-box {
  background-color: var(--bg-search);
  border-radius: var(--radius-xl);
  padding: var(--spacing-8) var(--spacing-16);
  transition: transform 0.2s ease;

  &:active {
    transform: scale(0.98);
  }
}

.mode-switch {
  background-color: var(--bg-primary-light);
  border-radius: var(--radius-xl);
  padding: var(--spacing-6) var(--spacing-12);
  transition: all 0.3s ease;

  &:active {
    transform: scale(0.95);
  }
}

.function-entrance {
  padding: var(--spacing-16) var(--spacing-10);

  .function-item {
    cursor: pointer;
    transition: all 0.2s ease;

    &:active {
      transform: scale(0.95);
    }

    .icon-wrapper {
      position: relative;
      transition: all 0.3s cubic-bezier(0.34, 1.56, 0.64, 1);

      &:active {
        transform: translateY(2rpx) scale(0.95);
        box-shadow: 0 4rpx 12rpx var(--shadow-color) !important;
      }

      &::before {
        content: "";
        position: absolute;
        top: -4rpx;
        left: -4rpx;
        right: -4rpx;
        bottom: -4rpx;
        border-radius: inherit;
        background: inherit;
        opacity: 0;
        transition: opacity 0.3s ease;
        z-index: -1;
      }

      &:hover::before {
        opacity: 0.1;
      }
    }

    .drop-shadow {
      filter: drop-shadow(0 2rpx 4rpx rgba(0, 0, 0, 0.2));
    }
  }
}

.function-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: var(--spacing-10);
  transition: transform 0.2s ease;

  &:active {
    transform: scale(0.95);
  }

  .icon-wrapper {
    width: 88rpx;
    height: 88rpx;
    border-radius: var(--radius-lg);
    display: flex;
    align-items: center;
    justify-content: center;
    margin-bottom: var(--spacing-6);
    box-shadow: var(--shadow-sm);
  }

  .text-26rpx {
    font-size: var(--font-size-sm);
    color: var(--text-secondary);
    font-weight: var(--font-weight-medium);
  }
}

.tabs-wrapper {
  background-color: #ffffff;
  padding: 0 var(--spacing-10);
  margin-bottom: var(--spacing-10);
  z-index: 10; /* 确保tabs在滚动时置顶 */
}

.gig-card-wrapper {
  margin: 20rpx;
  transition: transform 0.2s ease;

  &:active {
    transform: scale(0.98);
  }
}

/* 搜索面板样式 */
.search-panel {
  position: absolute;
  top: 100%;
  left: 0;
  right: 0;
  background-color: var(--bg-card);
  z-index: 100;
  padding: var(--spacing-16);
  box-shadow: var(--shadow-md);
  border-bottom-left-radius: var(--radius-lg);
  border-bottom-right-radius: var(--radius-lg);
}

.suggestion-item,
.history-item {
  display: flex;
  align-items: center;
  padding: var(--spacing-12) var(--spacing-8);
  font-size: var(--font-size-base);
  color: var(--text-base);

  &:active {
    background-color: var(--bg-hover);
  }
}

.history-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: var(--spacing-8) var(--spacing-8);
  margin-bottom: var(--spacing-8);
}

.history-title {
  font-size: var(--font-size-sm);
  color: var(--text-secondary);
  font-weight: var(--font-weight-medium);
}

.clear-history {
  font-size: var(--font-size-sm);
  color: var(--text-info);
  padding: var(--spacing-4) var(--spacing-8);
}

.no-history {
  padding: var(--spacing-24) 0;
  display: flex;
  justify-content: center;
  align-items: center;
}

.no-history-text {
  font-size: var(--font-size-sm);
  color: var(--text-grey);
}

.close-search {
  margin-top: var(--spacing-16);
  padding: var(--spacing-12) 0;
  text-align: center;
  font-size: var(--font-size-base);
  color: var(--text-primary);
  border-top: 1rpx solid var(--border-color);

  &:active {
    background-color: var(--bg-hover);
  }
}
</style>
