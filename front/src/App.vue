<script setup lang="ts">
import { onLaunch, onShow, onHide } from "@dcloudio/uni-app";
import { useGlobalStore } from "@/stores/global";
import { useUserStore } from "@/stores/user";
import AuthApi from "@/api/auth";

onLaunch(() => {
  console.log("App Launch");
  initializeApp();
});

// 应用初始化函数
const initializeApp = async () => {
  const globalStore = useGlobalStore();
  const userStore = useUserStore();
  console.log("initializeApp");

  // 1. 首先检查本地登录状态
  if (userStore.isLogin) {
    console.log("用户已登录，直接进入应用");
    return;
  }

  // 2. 用户未登录，准备显示隐私弹窗
  // 检查用户是否已同意隐私协议
  const hasAgreed = uni.getStorageSync("hasAgreedPrivacy");
  
  if (!hasAgreed) {
    // 预先检查用户状态，为弹窗准备数据
    try {
      const loginRes = await uni.login();
      // 将登录code保存到全局，供弹窗使用
      globalStore.setLoginCode(loginRes.code);
      
      // 检查用户注册状态
      const checkResult = await AuthApi.checkUserByCode({ code: loginRes.code });
      globalStore.setUserRegistered(checkResult.data.is_registered);
      
    } catch (error) {
      console.warn("预检查用户状态失败:", error);
      globalStore.setUserRegistered(false);
    }
    
    // 显示隐私弹窗
    globalStore.showPrivacyPopup();
  }
};

onShow(() => {
  console.log("App Show");
});
onHide(() => {
  console.log("App Hide");
});
</script>

<style>
/* 全局样式 */
@import "./styles/app.css";
</style>

<style lang="scss">
/* 第三方 UI 库 */
@import "@climblee/uv-ui/index.scss";

::v-deep .uni-nav-bar-text {
  font-size: 34rpx !important;
  font-weight: 500 !important;
  color: #212529;
}

::v-deep .uni-icons {
  font-size: 48rpx !important;
}
</style>
