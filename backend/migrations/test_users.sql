-- ====================================================================
-- 测试用户数据
-- 描述: 开发环境测试用户数据，仅在开发环境使用
-- 创建时间: 2025-07-23
-- ====================================================================

-- 插入测试用户数据（仅在开发环境使用）
-- 使用 ON CONFLICT 确保重复运行时不会出错

-- 1. 求职者测试账号
INSERT INTO users (
    uid, 
    openid, 
    phone, 
    nickname, 
    avatar, 
    gender, 
    is_verified, 
    personal_verification_id, 
    enterprise_id, 
    points, 
    source,
    created_at,
    updated_at
) VALUES (
    'test_user_001',
    'test_openid_001', 
    '13800138001',
    '测试用户-求职者',
    '/static/images/avatar-male.png',
    1,
    false,
    0,
    0,
    100,
    'dev-test',
    CURRENT_TIMESTAMP,
    CURRENT_TIMESTAMP
) ON CONFLICT (phone) DO UPDATE SET
    nickname = EXCLUDED.nickname,
    avatar = EXCLUDED.avatar,
    gender = EXCLUDED.gender,
    is_verified = EXCLUDED.is_verified,
    personal_verification_id = EXCLUDED.personal_verification_id,
    enterprise_id = EXCLUDED.enterprise_id,
    points = EXCLUDED.points,
    source = EXCLUDED.source,
    updated_at = CURRENT_TIMESTAMP;

-- 2. 企业招聘方测试账号
INSERT INTO users (
    uid,
    openid,
    phone,
    nickname,
    avatar,
    gender,
    is_verified,
    personal_verification_id,
    enterprise_id,
    points,
    source,
    created_at,
    updated_at
) VALUES (
    'test_user_002',
    'test_openid_002',
    '13800138002',
    '测试企业-招聘方',
    '/static/images/avatar-female.png',
    2,
    true,
    1,
    1,
    500,
    'dev-test',
    CURRENT_TIMESTAMP,
    CURRENT_TIMESTAMP
) ON CONFLICT (phone) DO UPDATE SET
    nickname = EXCLUDED.nickname,
    avatar = EXCLUDED.avatar,
    gender = EXCLUDED.gender,
    is_verified = EXCLUDED.is_verified,
    personal_verification_id = EXCLUDED.personal_verification_id,
    enterprise_id = EXCLUDED.enterprise_id,
    points = EXCLUDED.points,
    source = EXCLUDED.source,
    updated_at = CURRENT_TIMESTAMP;

-- 3. 管理员测试账号
INSERT INTO users (
    uid,
    openid,
    phone,
    nickname,
    avatar,
    gender,
    is_verified,
    personal_verification_id,
    enterprise_id,
    points,
    source,
    created_at,
    updated_at
) VALUES (
    'test_admin_003',
    'test_openid_003',
    '13800138003',
    '测试管理员',
    '/static/images/default-avatar.png',
    1,
    true,
    1,
    0,
    9999,
    'dev-test',
    CURRENT_TIMESTAMP,
    CURRENT_TIMESTAMP
) ON CONFLICT (phone) DO UPDATE SET
    nickname = EXCLUDED.nickname,
    avatar = EXCLUDED.avatar,
    gender = EXCLUDED.gender,
    is_verified = EXCLUDED.is_verified,
    personal_verification_id = EXCLUDED.personal_verification_id,
    enterprise_id = EXCLUDED.enterprise_id,
    points = EXCLUDED.points,
    source = EXCLUDED.source,
    updated_at = CURRENT_TIMESTAMP;

-- 验证插入结果
DO $$
BEGIN
    RAISE NOTICE '测试用户数据插入完成:';
    RAISE NOTICE '- 求职者测试账号: 13800138001';
    RAISE NOTICE '- 企业招聘方测试账号: 13800138002';
    RAISE NOTICE '- 管理员测试账号: 13800138003';
END $$;