package repository

import (
	"context"
	"fmt"
	"strings"
	"time"

	"bdb-backend/internal/constants"
	"bdb-backend/internal/model"
	"bdb-backend/internal/types"

	"gorm.io/gorm"
)

type JobRepository interface {
	// Job CRUD operations
	Create(ctx context.Context, job *model.Job) error
	Find(ctx context.Context, id uint) (*model.Job, error)
	Update(ctx context.Context, job *model.Job) error
	Delete(ctx context.Context, id uint) error
	GetList(ctx context.Context, req *types.JobQueryRequest) ([]*model.Job, int64, error)
	FindByUser(ctx context.Context, userID uint, status string) ([]*model.Job, error)
	FindByEnterprise(ctx context.Context, enterpriseID uint, status string) ([]*model.Job, error)

	// Job status operations
	UpdateStatus(ctx context.Context, id uint, status string) error
	RefreshJob(ctx context.Context, id uint) error
	GetExpiredJobs(ctx context.Context) ([]*model.Job, error)
	GetJobsForReview(ctx context.Context, page, pageSize int) ([]*model.Job, int64, error)

	// Job statistics
	IncrementViewCount(ctx context.Context, id uint) error
	GetJobStats(ctx context.Context, jobID uint) (*model.JobStatistics, error)
	GetEnterpriseJobStats(ctx context.Context, enterpriseID uint) (*model.JobStatistics, error)

	// Search and filter
	SearchJobs(ctx context.Context, req *types.JobQueryRequest) ([]*model.Job, int64, error)
	GetNearbyJobs(ctx context.Context, lat, lng float64, radius int, page, pageSize int) ([]*model.Job, int64, error)
	GetRecommendedJobs(ctx context.Context, userID uint, page, pageSize int) ([]*model.Job, int64, error)

	// Batch operations
	BatchUpdateStatus(ctx context.Context, ids []uint, status string) error
	CloseExpiredJobs(ctx context.Context) error

	// DB returns the underlying gorm.DB instance
	DB() *gorm.DB
}

type jobRepository struct {
	db *gorm.DB
}

func NewJobRepository(db *gorm.DB) JobRepository {
	return &jobRepository{db: db}
}

// DB returns the underlying gorm.DB instance
func (r *jobRepository) DB() *gorm.DB {
	return r.db
}

// Create creates a new job
func (r *jobRepository) Create(ctx context.Context, job *model.Job) error {
	return r.db.WithContext(ctx).Create(job).Error
}

// Find retrieves a job by ID with enterprise information
func (r *jobRepository) Find(ctx context.Context, id uint) (*model.Job, error) {
	var job model.Job
	err := r.db.WithContext(ctx).
		Select("id", "enterprise_id", "user_id", "title", "description", "status", "salary_min", "salary_max",
			"experience_req", "education_req", "work_type", "work_location", "latitude", "longitude",
			"remote_work_support", "benefits", "job_highlights", "requirements", "contact_method",
			"is_urgent", "urgent_expires_at", "last_refreshed_at", "today_refresh_count", "view_count",
			"application_count", "created_at", "updated_at").
		Preload("Enterprise", func(db *gorm.DB) *gorm.DB {
			return db.Select("id", "user_id", "name", "description", "logo_url", "type", "industry",
				"company_size", "contact_person", "contact_phone", "address", "latitude", "longitude",
				"welfare_tags", "is_verified", "verification_status", "active_job_count",
				"total_view_count", "created_at", "updated_at")
		}).
		First(&job, id).Error
	return &job, err
}

// Update updates a job
func (r *jobRepository) Update(ctx context.Context, job *model.Job) error {
	return r.db.WithContext(ctx).Save(job).Error
}

// Delete soft deletes a job
func (r *jobRepository) Delete(ctx context.Context, id uint) error {
	return r.db.WithContext(ctx).Delete(&model.Job{}, id).Error
}

// GetList retrieves a paginated list of jobs with filtering
func (r *jobRepository) GetList(ctx context.Context, req *types.JobQueryRequest) ([]*model.Job, int64, error) {
	var jobs []*model.Job
	var total int64

	db := r.db.WithContext(ctx).Model(&model.Job{}).
		Select("id", "enterprise_id", "user_id", "title", "description", "status", "salary_min", "salary_max",
			"work_type", "work_location", "is_urgent", "view_count", "application_count", "created_at", "updated_at").
		Preload("Enterprise", func(db *gorm.DB) *gorm.DB {
			return db.Select("id", "name", "logo_url", "type", "industry", "company_size", "address",
				"is_verified", "verification_status")
		})

	// Apply filters
	db = r.applyJobFilters(db, req)

	// Count total records
	if err := db.Count(&total).Error; err != nil {
		return nil, 0, err
	}

	// Apply sorting
	db = r.applyJobSorting(db, req)

	// Apply pagination
	err := db.Scopes(model.Paginate(req.Page, req.PageSize)).Find(&jobs).Error
	return jobs, total, err
}

// FindByUser retrieves jobs by user ID (enterprise owner)
func (r *jobRepository) FindByUser(ctx context.Context, userID uint, status string) ([]*model.Job, error) {
	var jobs []*model.Job
	query := r.db.WithContext(ctx).
		Joins("JOIN job_enterprises ON jobs.enterprise_id = job_enterprises.id").
		Where("job_enterprises.user_id = ?", userID).
		Preload("Enterprise")

	if status != "" {
		query = query.Where("jobs.status = ?", status)
	}

	err := query.Order("jobs.created_at DESC").Find(&jobs).Error
	return jobs, err
}

// FindByEnterprise retrieves jobs by enterprise ID
func (r *jobRepository) FindByEnterprise(ctx context.Context, enterpriseID uint, status string) ([]*model.Job, error) {
	var jobs []*model.Job
	query := r.db.WithContext(ctx).Where("enterprise_id = ?", enterpriseID).Preload("Enterprise")

	if status != "" {
		query = query.Where("status = ?", status)
	}

	err := query.Order("created_at DESC").Find(&jobs).Error
	return jobs, err
}

// UpdateStatus updates job status
func (r *jobRepository) UpdateStatus(ctx context.Context, id uint, status string) error {
	return r.db.WithContext(ctx).Model(&model.Job{}).
		Where("id = ?", id).
		Update("status", status).Error
}

// RefreshJob updates the last_refreshed_at field and today_refresh_count
func (r *jobRepository) RefreshJob(ctx context.Context, id uint) error {
	now := time.Now()
	return r.db.WithContext(ctx).Model(&model.Job{}).
		Where("id = ?", id).
		Updates(map[string]interface{}{
			"last_refreshed_at":   now,
			"today_refresh_count": gorm.Expr("today_refresh_count + 1"),
		}).Error
}

// GetExpiredJobs retrieves jobs that have expired
func (r *jobRepository) GetExpiredJobs(ctx context.Context) ([]*model.Job, error) {
	var jobs []*model.Job
	err := r.db.WithContext(ctx).
		Where("status = ? AND created_at < ?", constants.JobStatusActive, time.Now().AddDate(0, 0, -30)).
		Find(&jobs).Error
	return jobs, err
}

// GetJobsForReview retrieves jobs pending review
func (r *jobRepository) GetJobsForReview(ctx context.Context, page, pageSize int) ([]*model.Job, int64, error) {
	var jobs []*model.Job
	var total int64

	db := r.db.WithContext(ctx).Model(&model.Job{}).
		Where("status = ?", constants.JobStatusPendingReview).
		Preload("Enterprise")

	if err := db.Count(&total).Error; err != nil {
		return nil, 0, err
	}

	err := db.Scopes(model.Paginate(page, pageSize)).
		Order("created_at ASC").
		Find(&jobs).Error
	return jobs, total, err
}

// IncrementViewCount increments the view count for a job
func (r *jobRepository) IncrementViewCount(ctx context.Context, id uint) error {
	return r.db.WithContext(ctx).Model(&model.Job{}).
		Where("id = ?", id).
		Update("view_count", gorm.Expr("view_count + 1")).Error
}

// GetJobStats retrieves statistics for a specific job
func (r *jobRepository) GetJobStats(ctx context.Context, jobID uint) (*model.JobStatistics, error) {
	var stats model.JobStatistics

	// Get basic job info
	var job model.Job
	if err := r.db.WithContext(ctx).First(&job, jobID).Error; err != nil {
		return nil, err
	}

	stats.JobID = jobID
	stats.ViewCount = int64(job.ViewCount)

	// Count applications
	r.db.WithContext(ctx).Model(&model.JobApplication{}).
		Where("job_id = ?", jobID).
		Count(&stats.ApplicationCount)

	// Count favorites
	r.db.WithContext(ctx).Model(&model.JobFavorite{}).
		Where("job_id = ?", jobID).
		Count(&stats.FavoriteCount)

	return &stats, nil
}

// GetEnterpriseJobStats retrieves aggregated statistics for an enterprise
func (r *jobRepository) GetEnterpriseJobStats(ctx context.Context, enterpriseID uint) (*model.JobStatistics, error) {
	var stats model.JobStatistics

	// Count total jobs
	r.db.WithContext(ctx).Model(&model.Job{}).
		Where("enterprise_id = ?", enterpriseID).
		Count(&stats.TotalJobs)

	// Count active jobs
	r.db.WithContext(ctx).Model(&model.Job{}).
		Where("enterprise_id = ? AND status = ?", enterpriseID, constants.JobStatusActive).
		Count(&stats.ActiveJobs)

	// Sum view counts
	r.db.WithContext(ctx).Model(&model.Job{}).
		Where("enterprise_id = ?", enterpriseID).
		Select("COALESCE(SUM(view_count), 0)").
		Scan(&stats.ViewCount)

	// Count total applications
	r.db.WithContext(ctx).Model(&model.JobApplication{}).
		Joins("JOIN jobs ON job_applications.job_id = jobs.id").
		Where("jobs.enterprise_id = ?", enterpriseID).
		Count(&stats.ApplicationCount)

	return &stats, nil
}

// SearchJobs performs advanced job search
func (r *jobRepository) SearchJobs(ctx context.Context, req *types.JobQueryRequest) ([]*model.Job, int64, error) {
	var jobs []*model.Job
	var total int64

	db := r.db.WithContext(ctx).Model(&model.Job{}).Preload("Enterprise")

	// Only show active jobs for public search
	db = db.Where("status = ?", constants.JobStatusActive)

	// Apply search filters
	db = r.applyJobFilters(db, req)

	// Apply location-based search if provided
	if req.Latitude != 0 && req.Longitude != 0 && req.Radius > 0 {
		db = r.applyLocationFilter(db, req.Latitude, req.Longitude, req.Radius)
	}

	// Count total
	if err := db.Count(&total).Error; err != nil {
		return nil, 0, err
	}

	// Apply sorting
	db = r.applyJobSorting(db, req)

	// Apply pagination
	err := db.Scopes(model.Paginate(req.Page, req.PageSize)).Find(&jobs).Error
	return jobs, total, err
}

// GetNearbyJobs retrieves jobs within a geographic radius
func (r *jobRepository) GetNearbyJobs(ctx context.Context, lat, lng float64, radius int, page, pageSize int) ([]*model.Job, int64, error) {
	var jobs []*model.Job
	var total int64

	db := r.db.WithContext(ctx).Model(&model.Job{}).
		Where("status = ?", constants.JobStatusActive).
		Preload("Enterprise")

	// Apply location filter
	db = r.applyLocationFilter(db, lat, lng, radius)

	if err := db.Count(&total).Error; err != nil {
		return nil, 0, err
	}

	err := db.Scopes(model.Paginate(page, pageSize)).
		Order("created_at DESC").
		Find(&jobs).Error
	return jobs, total, err
}

// GetRecommendedJobs retrieves recommended jobs for a user
func (r *jobRepository) GetRecommendedJobs(ctx context.Context, userID uint, page, pageSize int) ([]*model.Job, int64, error) {
	var jobs []*model.Job
	var total int64

	// For now, return recent active jobs
	// TODO: Implement recommendation algorithm based on user preferences, resume, etc.
	db := r.db.WithContext(ctx).Model(&model.Job{}).
		Where("status = ?", constants.JobStatusActive).
		Preload("Enterprise")

	if err := db.Count(&total).Error; err != nil {
		return nil, 0, err
	}

	err := db.Scopes(model.Paginate(page, pageSize)).
		Order("created_at DESC").
		Find(&jobs).Error
	return jobs, total, err
}

// BatchUpdateStatus updates status for multiple jobs
func (r *jobRepository) BatchUpdateStatus(ctx context.Context, ids []uint, status string) error {
	return r.db.WithContext(ctx).Model(&model.Job{}).
		Where("id IN ?", ids).
		Update("status", status).Error
}

// CloseExpiredJobs closes jobs that have been active for too long
func (r *jobRepository) CloseExpiredJobs(ctx context.Context) error {
	thirtyDaysAgo := time.Now().AddDate(0, 0, -30)
	return r.db.WithContext(ctx).Model(&model.Job{}).
		Where("status = ? AND created_at < ?", constants.JobStatusActive, thirtyDaysAgo).
		Update("status", constants.JobStatusExpired).Error
}

// Helper methods for building queries

// applyJobFilters applies various filters to the job query
func (r *jobRepository) applyJobFilters(db *gorm.DB, req *types.JobQueryRequest) *gorm.DB {
	// Keyword search in title and description
	if req.Keywords != "" {
		keywords := "%" + strings.ToLower(req.Keywords) + "%"
		db = db.Where("LOWER(title) LIKE ? OR LOWER(description) LIKE ?", keywords, keywords)
	}

	// Salary range filter
	if req.SalaryMin > 0 {
		db = db.Where("salary_min >= ?", req.SalaryMin)
	}
	if req.SalaryMax > 0 {
		db = db.Where("salary_max <= ?", req.SalaryMax)
	}

	// Experience requirement filter
	if req.ExperienceReq > 0 {
		db = db.Where("experience_req <= ?", req.ExperienceReq)
	}

	// Education requirement filter
	if req.EducationReq > 0 {
		db = db.Where("education_req <= ?", req.EducationReq)
	}

	// Work type filter
	if req.WorkType > 0 {
		db = db.Where("work_type = ?", req.WorkType)
	}

	// Benefits filter
	if len(req.Benefits) > 0 {
		for _, benefit := range req.Benefits {
			db = db.Where("benefits @> ?", fmt.Sprintf("[\"%s\"]", benefit))
		}
	}

	// Include expired jobs filter
	if !req.IncludeExpired {
		db = db.Where("status != ?", constants.JobStatusExpired)
	}

	return db
}

// applyJobSorting applies sorting to the job query
func (r *jobRepository) applyJobSorting(db *gorm.DB, req *types.JobQueryRequest) *gorm.DB {
	sortBy := req.SortBy
	sortOrder := req.SortOrder

	// Default sorting
	if sortBy == "" {
		sortBy = "created_at"
	}
	if sortOrder == "" {
		sortOrder = "desc"
	}

	switch sortBy {
	case "created_at":
		db = db.Order(fmt.Sprintf("created_at %s", strings.ToUpper(sortOrder)))
	case "last_refreshed_at":
		db = db.Order(fmt.Sprintf("last_refreshed_at %s", strings.ToUpper(sortOrder)))
	case "salary_min":
		db = db.Order(fmt.Sprintf("salary_min %s", strings.ToUpper(sortOrder)))
	case "distance":
		// Distance sorting will be handled in location filter
		break
	default:
		db = db.Order("created_at DESC")
	}

	return db
}

// applyLocationFilter applies geographic filtering
func (r *jobRepository) applyLocationFilter(db *gorm.DB, lat, lng float64, radius int) *gorm.DB {
	// Using simple distance calculation (not precise but sufficient for small areas)
	// For production, consider using PostGIS ST_DWithin for better performance
	radiusInDegrees := float64(radius) / 111.0 // Rough conversion: 1 degree ≈ 111 km

	db = db.Where(
		"latitude BETWEEN ? AND ? AND longitude BETWEEN ? AND ?",
		lat-radiusInDegrees, lat+radiusInDegrees,
		lng-radiusInDegrees, lng+radiusInDegrees,
	)

	// Add distance calculation for ordering
	distanceSQL := fmt.Sprintf(
		"SQRT(POW(69.1 * (latitude - %f), 2) + POW(69.1 * (%f - longitude) * COS(latitude / 57.3), 2))",
		lat, lng,
	)

	db = db.Select("*, (" + distanceSQL + ") as distance")

	return db
}
