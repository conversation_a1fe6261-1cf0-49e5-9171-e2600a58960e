package model

import (
	"time"

	"gorm.io/datatypes"
)

// Job represents a job posting.
type Job struct {
	ID                uint           `gorm:"primaryKey;type:bigserial" json:"id"`
	EnterpriseID      uint           `gorm:"not null;comment:企业ID" json:"enterprise_id"`
	UserID            uint           `gorm:"not null;comment:发布者ID" json:"user_id"`
	Title             string         `gorm:"size:255;not null;comment:职位名称" json:"title"`
	Description       string         `gorm:"type:text;comment:职位描述" json:"description"`
	Status            string         `gorm:"type:varchar(20);not null;default:'pending_review';comment:职位状态" json:"status"`
	SalaryMin         int            `gorm:"not null;default:0;comment:最低薪资(元)" json:"salary_min"`
	SalaryMax         int            `gorm:"not null;default:0;comment:最高薪资(元)" json:"salary_max"`
	ExperienceReq     int16          `gorm:"type:smallint;not null;default:0;comment:经验要求" json:"experience_req"`
	EducationReq      int16          `gorm:"type:smallint;not null;default:0;comment:学历要求" json:"education_req"`
	WorkType          int16          `gorm:"type:smallint;not null;default:1;comment:工作类型" json:"work_type"`
	WorkLocation      string         `gorm:"size:200;comment:工作地点" json:"work_location"`
	Latitude          float64        `gorm:"type:decimal(10,7);default:0;comment:纬度" json:"latitude"`
	Longitude         float64        `gorm:"type:decimal(10,7);default:0;comment:经度" json:"longitude"`
	RemoteWorkSupport bool           `gorm:"default:false;comment:是否支持远程工作" json:"remote_work_support"`
	Benefits          datatypes.JSON `gorm:"type:text[];comment:福利待遇" json:"benefits"`
	JobHighlights     datatypes.JSON `gorm:"type:text[];comment:职位亮点" json:"job_highlights"`
	Requirements      datatypes.JSON `gorm:"type:text[];comment:任职要求" json:"requirements"`
	ContactMethod     string         `gorm:"type:varchar(20);default:'phone';comment:联系方式" json:"contact_method"`
	IsUrgent          bool           `gorm:"not null;default:false;comment:是否紧急招聘" json:"is_urgent"`
	UrgentExpiresAt   *time.Time     `gorm:"comment:紧急招聘过期时间" json:"urgent_expires_at"`
	LastRefreshedAt   time.Time      `gorm:"default:CURRENT_TIMESTAMP;comment:最后刷新时间" json:"last_refreshed_at"`
	TodayRefreshCount int            `gorm:"not null;default:0;comment:今日刷新次数" json:"today_refresh_count"`
	ViewCount         int            `gorm:"not null;default:0;comment:浏览次数" json:"view_count"`
	ApplicationCount  int            `gorm:"not null;default:0;comment:申请人数" json:"application_count"`
	SearchContent     string         `gorm:"type:text;comment:搜索内容" json:"search_content"`
	CreatedAt         time.Time      `gorm:"type:timestamp(0);default:CURRENT_TIMESTAMP" json:"created_at"`
	UpdatedAt         time.Time      `gorm:"type:timestamp(0);default:CURRENT_TIMESTAMP" json:"updated_at"`
	IsDel             int            `gorm:"not null;default:0;comment:软删除标志" json:"-"`

	// 关联字段
	Enterprise   *Enterprise      `gorm:"foreignKey:EnterpriseID" json:"enterprise,omitempty"`
	User         *User            `gorm:"foreignKey:UserID" json:"user,omitempty"`
	Applications []JobApplication `gorm:"foreignKey:JobID" json:"applications,omitempty"`

	// 虚拟字段
	HasApplied  bool    `gorm:"->:false;<-:false;comment:用户是否已申请" json:"has_applied"`
	IsFavorited bool    `gorm:"->:false;<-:false;comment:用户是否已收藏" json:"is_favorited"`
	Distance    float64 `gorm:"->:false;<-:false;comment:距离(km)" json:"distance,omitempty"`
}

// TableName returns the table name for the Job model.
func (Job) TableName() string {
	return "jobs"
}

// JobApplication represents a job application.
type JobApplication struct {
	ID               uint           `gorm:"primaryKey;type:bigserial" json:"id"`
	JobID            uint           `gorm:"not null;comment:职位ID" json:"job_id"`
	UserID           uint           `gorm:"not null;comment:申请者ID" json:"user_id"`
	Status           string         `gorm:"type:varchar(30);not null;default:'submitted';comment:申请状态" json:"status"`
	ResumeSnapshot   datatypes.JSON `gorm:"comment:简历快照" json:"resume_snapshot"`
	RecruiterNote    string         `gorm:"type:text;comment:招聘者备注" json:"recruiter_note"`
	InterviewTime    *time.Time     `gorm:"comment:面试时间" json:"interview_time"`
	InterviewAddress string         `gorm:"type:text;comment:面试地点" json:"interview_address"`
	CreatedAt        time.Time      `gorm:"type:timestamp(0);default:CURRENT_TIMESTAMP" json:"created_at"`
	UpdatedAt        time.Time      `gorm:"type:timestamp(0);default:CURRENT_TIMESTAMP" json:"updated_at"`

	// 关联字段
	Job  *Job  `gorm:"foreignKey:JobID" json:"job,omitempty"`
	User *User `gorm:"foreignKey:UserID" json:"user,omitempty"`
}

// TableName returns the table name for the JobApplication model.
func (JobApplication) TableName() string {
	return "job_applications"
}

// Favorite represents a user's favorite job.
type Favorite struct {
	ID        uint      `gorm:"primaryKey;type:bigserial" json:"id"`
	UserID    uint      `gorm:"not null;comment:用户ID" json:"user_id"`
	JobID     uint      `gorm:"not null;comment:职位ID" json:"job_id"`
	CreatedAt time.Time `gorm:"type:timestamp(0);default:CURRENT_TIMESTAMP" json:"created_at"`

	// 关联字段
	Job  *Job  `gorm:"foreignKey:JobID" json:"job,omitempty"`
	User *User `gorm:"foreignKey:UserID" json:"user,omitempty"`
}

// TableName returns the table name for the Favorite model.
func (Favorite) TableName() string {
	return "favorites"
}

// ViewHistory represents a user's job viewing history.
type ViewHistory struct {
	ID       uint      `gorm:"primaryKey;type:bigserial" json:"id"`
	UserID   uint      `gorm:"not null;comment:用户ID" json:"user_id"`
	JobID    uint      `gorm:"not null;comment:职位ID" json:"job_id"`
	ViewTime time.Time `gorm:"type:timestamp(0);default:CURRENT_TIMESTAMP;comment:浏览时间" json:"view_time"`

	// 关联字段
	Job  *Job  `gorm:"foreignKey:JobID" json:"job,omitempty"`
	User *User `gorm:"foreignKey:UserID" json:"user,omitempty"`
}

// TableName returns the table name for the ViewHistory model.
func (ViewHistory) TableName() string {
	return "view_history"
}

// JobNotification represents a job-related notification.
type JobNotification struct {
	ID        uint      `gorm:"primaryKey;type:bigserial" json:"id"`
	UserID    uint      `gorm:"not null;comment:用户ID" json:"user_id"`
	Title     string    `gorm:"size:255;not null;comment:通知标题" json:"title"`
	Content   string    `gorm:"type:text;comment:通知内容" json:"content"`
	Type      string    `gorm:"type:varchar(20);not null;default:'system';comment:通知类型" json:"type"`
	RelatedID *uint     `gorm:"comment:相关ID" json:"related_id"`
	IsRead    bool      `gorm:"default:false;comment:是否已读" json:"is_read"`
	CreatedAt time.Time `gorm:"type:timestamp(0);default:CURRENT_TIMESTAMP" json:"created_at"`

	// 关联字段
	User *User `gorm:"foreignKey:UserID" json:"user,omitempty"`
}

// TableName returns the table name for the JobNotification model.
func (JobNotification) TableName() string {
	return "job_notifications"
}

// Report represents a job or user report.
type Report struct {
	ID             uint      `gorm:"primaryKey;type:bigserial" json:"id"`
	ReporterUserID uint      `gorm:"not null;comment:举报人ID" json:"reporter_user_id"`
	ReportedJobID  *uint     `gorm:"comment:被举报职位ID" json:"reported_job_id"`
	ReportedUserID *uint     `gorm:"comment:被举报用户ID" json:"reported_user_id"`
	Reason         string    `gorm:"size:100;not null;comment:举报原因" json:"reason"`
	Description    string    `gorm:"type:text;comment:详细描述" json:"description"`
	Status         string    `gorm:"type:varchar(20);default:'pending';comment:处理状态" json:"status"`
	CreatedAt      time.Time `gorm:"type:timestamp(0);default:CURRENT_TIMESTAMP" json:"created_at"`
	UpdatedAt      time.Time `gorm:"type:timestamp(0);default:CURRENT_TIMESTAMP" json:"updated_at"`

	// 关联字段
	ReporterUser *User `gorm:"foreignKey:ReporterUserID" json:"reporter_user,omitempty"`
	ReportedJob  *Job  `gorm:"foreignKey:ReportedJobID" json:"reported_job,omitempty"`
	ReportedUser *User `gorm:"foreignKey:ReportedUserID" json:"reported_user,omitempty"`
}

// TableName returns the table name for the Report model.
func (Report) TableName() string {
	return "reports"
}

// ApplicationStatistics represents application statistics data
type ApplicationStatistics struct {
	UserID                uint  `json:"user_id"`
	JobID                 uint  `json:"job_id,omitempty"`
	EnterpriseID          uint  `json:"enterprise_id,omitempty"`
	TotalApplications     int64 `json:"total_applications"`
	PendingApplications   int64 `json:"pending_applications"`
	ViewedApplications    int64 `json:"viewed_applications"`
	InterviewApplications int64 `json:"interview_applications"`
	HiredApplications     int64 `json:"hired_applications"`
	RejectedApplications  int64 `json:"rejected_applications"`
}

// ApplicationTrend represents application trend data
type ApplicationTrend struct {
	Date  string `json:"date"`
	Count int64  `json:"count"`
}

// Type aliases for backward compatibility
type JobFavorite = Favorite
type JobViewHistory = ViewHistory
type JobReport = Report
